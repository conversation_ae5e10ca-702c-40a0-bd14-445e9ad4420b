#!/usr/bin/env python3
"""
WAV to OGG Opus converter
将WAV文件转换为标准OGG Opus格式的工具脚本
适用于ESP32的OGG Opus播放器
"""

import argparse
import os
import sys
import subprocess
from pathlib import Path

def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def analyze_audio_file(input_file):
    """分析音频文件信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_format', '-show_streams', input_file
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            
            # 查找音频流
            audio_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'audio':
                    audio_stream = stream
                    break
            
            if audio_stream:
                format_info = data.get('format', {})
                duration = float(format_info.get('duration', 0))
                file_size = int(format_info.get('size', 0))
                
                print(f"音频文件信息: {input_file}")
                print(f"  时长: {duration:.2f} 秒")
                print(f"  采样率: {audio_stream.get('sample_rate', 'Unknown')} Hz")
                print(f"  声道数: {audio_stream.get('channels', 'Unknown')}")
                print(f"  位深: {audio_stream.get('bits_per_sample', 'Unknown')} bit")
                print(f"  文件大小: {file_size:,} 字节")
                return True
        
        return False
    except Exception as e:
        print(f"分析文件失败: {e}")
        return False

def convert_wav_to_ogg_opus(input_file, output_file, bitrate=32000, sample_rate=16000, channels=1):
    """
    将WAV文件转换为OGG Opus格式
    
    Args:
        input_file: 输入WAV文件路径
        output_file: 输出OGG Opus文件路径
        bitrate: 比特率 (默认32kbps，适合语音)
        sample_rate: 采样率 (默认16kHz，适合语音)
        channels: 声道数 (默认1，单声道)
    """
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return False
    
    print(f"正在转换: {input_file} -> {output_file}")
    print(f"参数: 采样率={sample_rate}Hz, 声道={channels}, 比特率={bitrate}bps")
    print(f"输出格式: OGG Opus容器")
    
    try:
        # 获取输入文件大小
        input_size = os.path.getsize(input_file)
        
        # 构建ffmpeg命令
        cmd = [
            'ffmpeg', '-y',  # 覆盖输出文件
            '-i', input_file,  # 输入文件
            '-c:a', 'libopus',  # 使用Opus编码器
            '-b:a', str(bitrate),  # 比特率
            '-ar', str(sample_rate),  # 采样率
            '-ac', str(channels),  # 声道数
            '-application', 'voip',  # 针对语音优化
            '-frame_duration', '60',  # 60ms帧长度
            '-packet_loss', '1',  # 启用丢包恢复
            '-f', 'ogg',  # 输出OGG容器格式
            output_file
        ]
        
        print("执行转换...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # 获取输出文件大小
            output_size = os.path.getsize(output_file)
            compression_ratio = (1 - output_size / input_size) * 100
            
            print("转换完成!")
            print(f"输入文件大小: {input_size:,} 字节")
            print(f"输出文件大小: {output_size:,} 字节")
            print(f"压缩率: {compression_ratio:.1f}%")
            return True
        else:
            print(f"转换失败:")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("错误: 转换超时")
        return False
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description='将WAV文件转换为OGG Opus格式（适用于ESP32）',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本转换（推荐语音设置）
  python wav_to_ogg_opus.py speech.wav speech.ogg
  
  # 高质量音乐转换
  python wav_to_ogg_opus.py music.wav music.ogg --bitrate 64000 --sample-rate 48000 --channels 2
  
  # 低带宽语音转换
  python wav_to_ogg_opus.py voice.wav voice.ogg --bitrate 24000
  
  # 分析音频文件
  python wav_to_ogg_opus.py --analyze music.wav
        """
    )
    
    parser.add_argument('input', nargs='?', help='输入WAV文件路径')
    parser.add_argument('output', nargs='?', help='输出OGG Opus文件路径')
    
    parser.add_argument('--bitrate', '-b', type=int, default=32000,
                       help='比特率 (默认: 32000bps，适合语音)')
    parser.add_argument('--sample-rate', '-s', type=int, default=16000,
                       help='采样率 (默认: 16000Hz，适合语音)')
    parser.add_argument('--channels', '-c', type=int, default=1,
                       help='声道数 (默认: 1，单声道)')
    parser.add_argument('--analyze', '-a', action='store_true',
                       help='仅分析输入文件信息')
    
    args = parser.parse_args()
    
    # 检查ffmpeg
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg")
        print("请安装ffmpeg:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Windows: 下载ffmpeg并添加到PATH")
        sys.exit(1)
    
    if not args.input:
        parser.print_help()
        sys.exit(1)
    
    # 仅分析文件
    if args.analyze:
        if analyze_audio_file(args.input):
            sys.exit(0)
        else:
            sys.exit(1)
    
    # 如果没有指定输出文件，自动生成
    if args.output is None:
        input_path = Path(args.input)
        args.output = str(input_path.with_suffix('.ogg'))
    
    print(f"ESP32 OGG Opus转换器")
    print(f"输入: {args.input}")
    print(f"输出: {args.output}")
    
    success = convert_wav_to_ogg_opus(
        args.input, 
        args.output, 
        bitrate=args.bitrate,
        sample_rate=args.sample_rate,
        channels=args.channels
    )
    
    if success:
        print("\n✓ 转换成功!")
        print(f"生成的OGG Opus文件可以直接用于ESP32播放")
        sys.exit(0)
    else:
        print("\n✗ 转换失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
