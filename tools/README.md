# WAV to Opus Converter

这是一个将WAV文件转换为Opus格式的Python工具脚本。

## 安装依赖

首先需要安装opuslib库：

```bash
pip install opuslib
```

## 使用方法

### 基本用法

```bash
# 转换WAV文件为Opus格式（自动生成输出文件名）
python wav_to_opus.py input.wav

# 指定输出文件名
python wav_to_opus.py input.wav output.opus
```

### 高级选项

```bash
# 设置比特率（默认64kbps）
python wav_to_opus.py input.wav output.opus --bitrate 128000

# 设置帧大小（默认960样本）
python wav_to_opus.py input.wav output.opus --frame-size 480

# 查看帮助
python wav_to_opus.py --help
```

## 参数说明

- `input`: 输入的WAV文件路径
- `output`: 输出的Opus文件路径（可选，默认为输入文件名.opus）
- `--bitrate, -b`: 比特率，单位bps（默认64000）
- `--frame-size, -f`: 帧大小，单位样本（默认960）

## 支持的格式

- **输入格式**: 16位WAV文件
- **声道**: 单声道或立体声
- **采样率**: 任意采样率（推荐48kHz）

## 示例

```bash
# 转换单声道WAV文件
python wav_to_opus.py mono_audio.wav

# 转换立体声WAV文件，使用128kbps比特率
python wav_to_opus.py stereo_audio.wav --bitrate 128000

# 转换并指定输出文件名
python wav_to_opus.py input.wav compressed_audio.opus
```

## 注意事项

1. 只支持16位WAV文件
2. 只支持单声道或立体声
3. 输出的Opus文件可以直接用于HTTP流播放
4. 建议使用48kHz采样率以获得最佳质量

## 与ESP32播放器的兼容性

转换后的Opus文件可以直接通过HTTP服务器提供给ESP32播放器使用：

```bash
# 启动简单的HTTP服务器
python -m http.server 8000

# ESP32可以通过以下URL播放
# http://your-server-ip:8000/output.opus
```

修改后的ESP32播放器会直接处理Opus数据，无需OGG容器格式。
