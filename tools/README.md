# WAV to Opus Converter

这是一个将WAV文件转换为Opus格式的Python工具脚本。

## 安装依赖

首先需要安装opuslib库：

```bash
pip install opuslib
```

## 使用方法

### 基本用法

```bash
# 转换WAV文件为纯Opus流（推荐用于ESP32）
python wav_to_opus.py input.wav output.opus --raw

# 转换为OGG容器格式
python wav_to_opus.py input.wav output.ogg
```

### ESP32专用命令（推荐）

```bash
# 语音文件转换（32kbps，纯Opus流）
python wav_to_opus.py speech.wav speech.opus --raw --bitrate 32000

# 音乐文件转换（48kbps，纯Opus流）
python wav_to_opus.py music.wav music.opus --raw --bitrate 48000
```

### 高级选项

```bash
# 设置比特率（默认32kbps）
python wav_to_opus.py input.wav output.opus --raw --bitrate 48000

# 设置帧大小（默认960样本）
python wav_to_opus.py input.wav output.opus --raw --frame-size 480

# 查看帮助
python wav_to_opus.py --help
```

## 参数说明

- `input`: 输入的WAV文件路径
- `output`: 输出的Opus文件路径（可选，默认为输入文件名.opus）
- `--raw, -r`: 输出纯Opus流（无OGG容器，**推荐用于ESP32**）
- `--bitrate, -b`: 比特率，单位bps（默认32000）
- `--frame-size, -f`: 帧大小，单位样本（默认960）

## 支持的格式

- **输入格式**: 16位WAV文件
- **声道**: 单声道或立体声
- **采样率**: 任意采样率（推荐48kHz）

## 示例

```bash
# ESP32推荐用法 - 转换语音文件
python wav_to_opus.py speech.wav speech.opus --raw --bitrate 32000

# ESP32推荐用法 - 转换音乐文件
python wav_to_opus.py music.wav music.opus --raw --bitrate 48000

# 转换为OGG格式（其他播放器使用）
python wav_to_opus.py input.wav output.ogg --bitrate 64000
```

## 注意事项

1. 只支持16位WAV文件
2. 只支持单声道或立体声
3. 输出的Opus文件可以直接用于HTTP流播放
4. 建议使用48kHz采样率以获得最佳质量

## 与ESP32播放器的兼容性

转换后的Opus文件可以直接通过HTTP服务器提供给ESP32播放器使用：

```bash
# 启动简单的HTTP服务器
python -m http.server 8000

# ESP32可以通过以下URL播放
# http://your-server-ip:8000/output.opus
```

修改后的ESP32播放器会直接处理Opus数据，无需OGG容器格式。
