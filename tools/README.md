# WAV to OGG Opus Converter

这是一个将WAV文件转换为OGG Opus格式的Python工具脚本，专为ESP32音频播放器设计。

## 安装依赖

需要安装ffmpeg：

```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# 下载ffmpeg并添加到PATH环境变量
```

## 使用方法

### 基本用法

```bash
# 转换WAV文件为OGG Opus格式（推荐用于ESP32）
python wav_to_ogg_opus.py input.wav output.ogg

# 自动生成输出文件名
python wav_to_ogg_opus.py input.wav
```

### ESP32专用命令（推荐）

```bash
# 语音文件转换（32kbps，16kHz，单声道）
python wav_to_ogg_opus.py speech.wav speech.ogg --bitrate 32000

# 音乐文件转换（64kbps，48kHz，立体声）
python wav_to_ogg_opus.py music.wav music.ogg --bitrate 64000 --sample-rate 48000 --channels 2

# 低带宽语音转换（24kbps）
python wav_to_ogg_opus.py voice.wav voice.ogg --bitrate 24000
```

### 高级选项

```bash
# 设置比特率（默认32kbps）
python wav_to_ogg_opus.py input.wav output.ogg --bitrate 48000

# 设置采样率和声道数
python wav_to_ogg_opus.py input.wav output.ogg --sample-rate 48000 --channels 2

# 分析音频文件信息
python wav_to_ogg_opus.py --analyze input.wav

# 查看帮助
python wav_to_ogg_opus.py --help
```

## 参数说明

- `input`: 输入的WAV文件路径
- `output`: 输出的OGG Opus文件路径（可选，默认为输入文件名.ogg）
- `--bitrate, -b`: 比特率，单位bps（默认32000，适合语音）
- `--sample-rate, -s`: 采样率，单位Hz（默认16000，适合语音）
- `--channels, -c`: 声道数（默认1，单声道）
- `--analyze, -a`: 仅分析输入文件信息，不进行转换

## 支持的格式

- **输入格式**: WAV文件（任意位深和采样率）
- **输出格式**: OGG Opus容器格式
- **声道**: 单声道或立体声
- **采样率**: 自动转换到指定采样率

## 示例

```bash
# ESP32推荐用法 - 转换语音文件
python wav_to_ogg_opus.py speech.wav speech.ogg --bitrate 32000

# ESP32推荐用法 - 转换音乐文件
python wav_to_ogg_opus.py music.wav music.ogg --bitrate 64000 --sample-rate 48000 --channels 2

# 分析音频文件
python wav_to_ogg_opus.py --analyze music.wav
```

## 注意事项

1. 只支持16位WAV文件
2. 只支持单声道或立体声
3. 输出的Opus文件可以直接用于HTTP流播放
4. 建议使用48kHz采样率以获得最佳质量

## 与ESP32播放器的兼容性

转换后的OGG Opus文件可以直接通过HTTP服务器提供给ESP32播放器使用：

```bash
# 启动简单的HTTP服务器
python -m http.server 8000

# ESP32可以通过以下URL播放
# http://your-server-ip:8000/music.ogg
```

ESP32播放器会自动解析OGG容器，提取Opus音频数据进行播放。

## 推荐的比特率设置

| 应用场景 | 比特率 | 采样率 | 声道 | 命令示例 |
|---------|--------|--------|------|----------|
| 语音通话 | 24-32kbps | 16kHz | 单声道 | `--bitrate 32000 --sample-rate 16000 --channels 1` |
| 高质量语音 | 48kbps | 16kHz | 单声道 | `--bitrate 48000 --sample-rate 16000 --channels 1` |
| 音乐播放 | 64-96kbps | 48kHz | 立体声 | `--bitrate 64000 --sample-rate 48000 --channels 2` |
| 高质量音乐 | 128kbps | 48kHz | 立体声 | `--bitrate 128000 --sample-rate 48000 --channels 2` |
