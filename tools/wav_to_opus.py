#!/usr/bin/env python3
"""
WAV to Opus converter using opuslib
将WAV文件转换为Opus格式的工具脚本
"""

import argparse
import wave
import sys
import os
from pathlib import Path

try:
    import opuslib
except ImportError:
    print("Error: opuslib not installed. Please install it with:")
    print("pip install opuslib")
    sys.exit(1)

def convert_wav_to_opus(input_file, output_file, bitrate=64000, frame_size=960):
    """
    将WAV文件转换为Opus格式
    
    Args:
        input_file: 输入WAV文件路径
        output_file: 输出Opus文件路径
        bitrate: 比特率 (默认64kbps)
        frame_size: 帧大小 (默认960样本，对应48kHz下20ms)
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found")
        return False
    
    try:
        # 打开WAV文件
        with wave.open(input_file, 'rb') as wav_file:
            # 获取WAV文件参数
            channels = wav_file.getnchannels()
            sample_rate = wav_file.getframerate()
            sample_width = wav_file.getsampwidth()
            frames = wav_file.getnframes()
            
            print(f"Input WAV info:")
            print(f"  Channels: {channels}")
            print(f"  Sample rate: {sample_rate} Hz")
            print(f"  Sample width: {sample_width} bytes")
            print(f"  Duration: {frames / sample_rate:.2f} seconds")
            
            # 检查参数兼容性
            if sample_width != 2:
                print(f"Error: Only 16-bit WAV files are supported (got {sample_width * 8}-bit)")
                return False
            
            if channels not in [1, 2]:
                print(f"Error: Only mono or stereo WAV files are supported (got {channels} channels)")
                return False
            
            # 创建Opus编码器
            encoder = opuslib.Encoder(
                fs=sample_rate,
                channels=channels,
                application=opuslib.APPLICATION_AUDIO
            )
            
            # 设置比特率
            encoder.bitrate = bitrate
            print(f"Opus encoder settings:")
            print(f"  Sample rate: {sample_rate} Hz")
            print(f"  Channels: {channels}")
            print(f"  Bitrate: {bitrate} bps")
            print(f"  Frame size: {frame_size} samples")
            
            # 读取所有音频数据
            audio_data = wav_file.readframes(frames)
            
        # 转换为16位整数数组
        import struct
        if channels == 1:
            audio_samples = struct.unpack(f'<{frames}h', audio_data)
        else:
            audio_samples = struct.unpack(f'<{frames * channels}h', audio_data)
        
        # 创建输出文件
        with open(output_file, 'wb') as opus_file:
            # 按帧编码
            total_frames = len(audio_samples) // (frame_size * channels)
            encoded_frames = 0
            
            for i in range(0, len(audio_samples), frame_size * channels):
                # 获取当前帧的数据
                frame_data = audio_samples[i:i + frame_size * channels]
                
                # 如果帧不完整，用零填充
                if len(frame_data) < frame_size * channels:
                    frame_data = list(frame_data) + [0] * (frame_size * channels - len(frame_data))
                
                # 转换为字节
                frame_bytes = struct.pack(f'<{len(frame_data)}h', *frame_data)
                
                # 编码为Opus
                try:
                    opus_data = encoder.encode(frame_bytes, frame_size)
                    opus_file.write(opus_data)
                    encoded_frames += 1
                    
                    # 显示进度
                    if encoded_frames % 100 == 0:
                        progress = (encoded_frames / total_frames) * 100
                        print(f"Progress: {progress:.1f}% ({encoded_frames}/{total_frames} frames)")
                        
                except Exception as e:
                    print(f"Warning: Failed to encode frame {encoded_frames}: {e}")
                    continue
            
            print(f"Conversion completed!")
            print(f"  Encoded frames: {encoded_frames}")
            print(f"  Output file: {output_file}")
            print(f"  Output size: {os.path.getsize(output_file)} bytes")
            
        return True
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Convert WAV files to Opus format')
    parser.add_argument('input', help='Input WAV file path')
    parser.add_argument('output', nargs='?', help='Output Opus file path (optional)')
    parser.add_argument('--bitrate', '-b', type=int, default=64000,
                       help='Bitrate in bps (default: 64000)')
    parser.add_argument('--frame-size', '-f', type=int, default=960,
                       help='Frame size in samples (default: 960)')
    
    args = parser.parse_args()
    
    # 如果没有指定输出文件，自动生成
    if args.output is None:
        input_path = Path(args.input)
        args.output = str(input_path.with_suffix('.opus'))
    
    print(f"Converting '{args.input}' to '{args.output}'...")
    
    success = convert_wav_to_opus(
        args.input, 
        args.output, 
        bitrate=args.bitrate,
        frame_size=args.frame_size
    )
    
    if success:
        print("Conversion successful!")
        sys.exit(0)
    else:
        print("Conversion failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
