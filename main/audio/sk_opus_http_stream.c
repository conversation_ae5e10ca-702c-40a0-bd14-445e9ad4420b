/**
 * @file: sk_opus_http_stream.c
 * @description: 简化的Opus HTTP流播放器
 */
#include "sk_opus_http_stream.h"
#include "sk_opus_dec.h"
#include "sk_os.h"
#include "sk_log.h"
#include "esp_http_client.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/ringbuf.h"
#include <string.h>

static const char *TAG = "OpusHttp";

// 优化配置
#define MIN_BUFFER (32 * 1024)          // 32KB开始播放（降低延迟）
#define RINGBUF_SIZE (256 * 1024)       // 256KB环形缓冲区（增大缓冲）

// 简化的控制结构
typedef struct {
    char url[256];                      // 音频URL
    RingbufHandle_t ringbuf;            // 环形缓冲区
    esp_http_client_handle_t client;    // HTTP客户端
    TaskHandle_t download_task;         // 下载任务
    SemaphoreHandle_t mutex;            // 互斥锁

    volatile bool playing;              // 播放状态
    volatile bool download_done;        // 下载完成
    volatile size_t downloaded_bytes;   // 已下载字节数
    volatile bool ogg_header_skipped;   // OGG头部是否已跳过
} OpusHttpCtrl;

static OpusHttpCtrl g_ctrl = {0};

// 简化的OGG头部跳过函数
static size_t skip_ogg_headers(const uint8_t *data, size_t len) {
    size_t offset = 0;

    // 寻找第一个音频数据页面
    while (offset + 27 < len) {
        // 查找OGG页面标识 "OggS"
        if (memcmp(data + offset, "OggS", 4) == 0) {
            // 获取段数量
            uint8_t segments = data[offset + 26];
            if (offset + 27 + segments >= len) {
                break; // 数据不完整
            }

            // 计算页面大小
            size_t page_size = 27 + segments;
            for (int i = 0; i < segments; i++) {
                page_size += data[offset + 27 + i];
            }

            if (offset + page_size > len) {
                break; // 页面不完整
            }

            // 检查是否是头部页面
            const uint8_t *payload = data + offset + 27 + segments;
            if (segments > 0 && offset + 27 + segments + 8 <= len) {
                if (memcmp(payload, "OpusHead", 8) == 0 ||
                    memcmp(payload, "OpusTags", 8) == 0) {
                    // 跳过头部页面
                    offset += page_size;
                    continue;
                }
            }

            // 找到音频数据页面
            return offset;
        }
        offset++;
    }

    return len; // 没找到有效数据
}


// 使用ESP-IDF环形缓冲区简化数据操作
static size_t get_available_data_size(void) {
    if (!g_ctrl.ringbuf) return 0;

    UBaseType_t items_waiting;
    vRingbufferGetInfo(g_ctrl.ringbuf, NULL, NULL, NULL, NULL, &items_waiting);
    return items_waiting;
}

// 从环形缓冲区读取数据
static size_t read_opus_data(uint8_t *data, size_t max_len) {
    if (!g_ctrl.ringbuf || !g_ctrl.playing) {
        return 0;
    }

    size_t item_size;
    uint8_t *item = (uint8_t *)xRingbufferReceive(g_ctrl.ringbuf, &item_size, 0);

    if (item != NULL) {
        size_t copy_size = (item_size > max_len) ? max_len : item_size;
        memcpy(data, item, copy_size);
        vRingbufferReturnItem(g_ctrl.ringbuf, item);
        return copy_size;
    }

    return 0;
}

// 向环形缓冲区写入数据（简化OGG处理）
static bool write_opus_data(const uint8_t *data, size_t len) {
    if (!g_ctrl.ringbuf || len == 0) {
        return false;
    }

    const uint8_t *write_data = data;
    size_t write_len = len;

    // 如果还没跳过OGG头部，先处理
    if (!g_ctrl.ogg_header_skipped) {
        size_t skip_bytes = skip_ogg_headers(data, len);
        if (skip_bytes > 0 && skip_bytes < len) {
            ESP_LOGI(TAG, "Skipped OGG headers: %d bytes", skip_bytes);
            write_data = data + skip_bytes;
            write_len = len - skip_bytes;
            g_ctrl.ogg_header_skipped = true;
        } else if (skip_bytes >= len) {
            // 全部是头部数据，跳过
            g_ctrl.downloaded_bytes += len;
            return true;
        }
    }

    // 发送数据到环形缓冲区
    if (write_len > 0) {
        if (xRingbufferSend(g_ctrl.ringbuf, write_data, write_len, pdMS_TO_TICKS(10)) == pdTRUE) {
            g_ctrl.downloaded_bytes += len;
            return true;
        } else {
            // 缓冲区满，稍微等待后重试
            vTaskDelay(pdMS_TO_TICKS(5));
            if (xRingbufferSend(g_ctrl.ringbuf, write_data, write_len, pdMS_TO_TICKS(5)) == pdTRUE) {
                g_ctrl.downloaded_bytes += len;
                return true;
            } else {
                ESP_LOGW(TAG, "Buffer full, dropped %d bytes", write_len);
                return false;
            }
        }
    }

    g_ctrl.downloaded_bytes += len;
    return true;
}

static esp_err_t http_event_handler(esp_http_client_event_t *evt) {
    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            if (evt->data_len > 0) {
                // 写入环形缓冲区
                if (write_opus_data((uint8_t *)evt->data, evt->data_len)) {
                    // 缓冲足够时开始播放
                    if (!g_ctrl.playing && get_available_data_size() >= MIN_BUFFER) {
                        g_ctrl.playing = true;
                        ESP_LOGI(TAG, "Start playing (buffered: %d KB)",
                                get_available_data_size() / 1024);
                    }
                }
            }
            break;

        case HTTP_EVENT_ON_FINISH:
            g_ctrl.download_done = true;
            ESP_LOGI(TAG, "Download completed: %d bytes", g_ctrl.downloaded_bytes);
            break;

        case HTTP_EVENT_ERROR:
            ESP_LOGE(TAG, "HTTP error");
            break;

        default:
            break;
    }
    return ESP_OK;
}

// 简化的下载任务，适配简单的python http.server
static void download_task(void *arg) {
    // 简单的HTTP客户端配置
    esp_http_client_config_t config = {
        .url = g_ctrl.url,
        .event_handler = http_event_handler,
        .timeout_ms = 30000,
        .buffer_size = 4096,
    };

    g_ctrl.client = esp_http_client_init(&config);
    if (!g_ctrl.client) {
        ESP_LOGE(TAG, "Failed to init HTTP client");
        g_ctrl.download_task = NULL;
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "Downloading: %s", g_ctrl.url);

    // 执行HTTP请求
    esp_err_t err = esp_http_client_perform(g_ctrl.client);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "HTTP request failed: %s", esp_err_to_name(err));
    }

    // 清理
    esp_http_client_cleanup(g_ctrl.client);
    g_ctrl.client = NULL;
    g_ctrl.download_task = NULL;
    vTaskDelete(NULL);
}

// 优化的初始化函数，使用ESP-IDF环形缓冲区
int32_t SkOpusHttpInit(void) {
    if (g_ctrl.ringbuf != NULL) {
        ESP_LOGW(TAG, "Already initialized");
        return SK_RET_SUCCESS;
    }

    // 清零控制结构
    memset(&g_ctrl, 0, sizeof(g_ctrl));

    // 创建环形缓冲区（使用PSRAM）
    g_ctrl.ringbuf = xRingbufferCreate(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF);
    if (!g_ctrl.ringbuf) {
        ESP_LOGE(TAG, "Failed to create ring buffer");
        return SK_RET_NO_MEMORY;
    }

    // 创建互斥锁
    g_ctrl.mutex = xSemaphoreCreateMutex();
    if (!g_ctrl.mutex) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = NULL;
        ESP_LOGE(TAG, "Failed to create mutex");
        return SK_RET_FAIL;
    }

    ESP_LOGI(TAG, "Initialized (Ring buffer: %d KB)", RINGBUF_SIZE / 1024);
    return SK_RET_SUCCESS;
}

void SkOpusHttpDeinit(void) {
    // 停止播放
    SkOpusHttpStop();

    // 清理环形缓冲区
    if (g_ctrl.ringbuf) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = NULL;
    }

    // 清理互斥锁
    if (g_ctrl.mutex) {
        vSemaphoreDelete(g_ctrl.mutex);
        g_ctrl.mutex = NULL;
    }

    // 清零控制结构
    memset(&g_ctrl, 0, sizeof(g_ctrl));
    ESP_LOGI(TAG, "Deinitialized");
}

int32_t SkOpusHttpPlay(const char* url) {
    if (!url || strlen(url) >= sizeof(g_ctrl.url)) {
        ESP_LOGE(TAG, "Invalid URL");
        return SK_RET_INVALID_PARAM;
    }

    if (!g_ctrl.ringbuf) {
        ESP_LOGE(TAG, "Not initialized");
        return SK_RET_NOT_INITIALIZED;
    }

    // 停止当前播放
    SkOpusHttpStop();

    // 重置状态
    strncpy(g_ctrl.url, url, sizeof(g_ctrl.url) - 1);
    g_ctrl.url[sizeof(g_ctrl.url) - 1] = '\0';
    g_ctrl.playing = false;
    g_ctrl.download_done = false;
    g_ctrl.downloaded_bytes = 0;
    g_ctrl.ogg_header_skipped = false;  // 重置OGG头部跳过标志

    // 清空环形缓冲区（重新创建）
    if (g_ctrl.ringbuf) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = xRingbufferCreate(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF);
    }

    // 创建下载任务
    if (xTaskCreate(download_task, "OpusHttpDL", 8192, NULL, 5, &g_ctrl.download_task) != pdPASS) {
        ESP_LOGE(TAG, "Failed to create download task");
        return SK_RET_FAIL;
    }

    ESP_LOGI(TAG, "Started streaming: %s", url);

    // 添加调试信息
    ESP_LOGI(TAG, "Debug: playing=%d, ringbuf=%p", g_ctrl.playing, g_ctrl.ringbuf);

    return SK_RET_SUCCESS;
}

int32_t SkOpusHttpStop(void) {
    // 停止播放
    g_ctrl.playing = false;

    // 停止HTTP客户端
    if (g_ctrl.client) {
        esp_http_client_close(g_ctrl.client);
    }

    // 停止下载任务
    if (g_ctrl.download_task) {
        vTaskDelete(g_ctrl.download_task);
        g_ctrl.download_task = NULL;
    }

    ESP_LOGI(TAG, "Stopped");
    return SK_RET_SUCCESS;
}

size_t SkOpusHttpFeedAudio(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    static uint32_t call_count = 0;
    call_count++;

    // 每100次调用打印一次调试信息
    if (call_count % 100 == 1) {
        ESP_LOGI(TAG, "Audio callback called %u times, playing=%d, ringbuf=%p",
                call_count, g_ctrl.playing, g_ctrl.ringbuf);
    }

    // 检查播放状态
    if (!g_ctrl.playing || !g_ctrl.ringbuf) {
        memset(data, 0, len);
        return len;
    }

    // 从环形缓冲区读取Opus数据
    uint8_t opus_data[960];  // 减小缓冲区，避免读取过多数据
    size_t read_len = read_opus_data(opus_data, sizeof(opus_data));

    if (read_len > 0) {
        // 为Opus数据添加4字节头部（兼容现有解码器）
        uint8_t opus_with_header[964];  // 960 + 4字节头部
        if (read_len <= 960) {
            // 添加4字节头部（WebSocket格式兼容）
            opus_with_header[0] = 0x00;
            opus_with_header[1] = 0x00;
            opus_with_header[2] = 0x00;
            opus_with_header[3] = 0x00;
            memcpy(opus_with_header + 4, opus_data, read_len);

            // 送入Opus解码器
            SkAudioDownlinkTimeRecord record = {0};
            int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0, opus_with_header, read_len + 4, &record);

            if (ret != SK_RET_SUCCESS) {
                ESP_LOGI(TAG, "Opus decode failed: %d (len=%d)", ret, read_len + 4);
            } else {
                if (call_count % 50 == 1) {
                    ESP_LOGI(TAG, "Opus data sent to decoder: %d bytes (with header)", read_len + 4);
                }
            }
        }
    } else {
        // 没有数据时检查是否下载完成
        if (g_ctrl.download_done && get_available_data_size() == 0) {
            ESP_LOGI(TAG, "Playback finished");
            g_ctrl.playing = false;
        }
    }

    // 从Opus解码器获取PCM数据
    size_t pcm_len = SkOpusDecFeedPlayAudio(data, len, timeRecord);
    if (pcm_len > 0) {
        if (call_count % 50 == 1) {
            ESP_LOGI(TAG, "Got PCM data: %d bytes", pcm_len);
        }
    } else {
        if (call_count % 100 == 1) {
            ESP_LOGI(TAG, "No PCM data available");
            // 显示解码器统计信息
            SkOpusDecStat();
        }
    }
    return pcm_len;
}
