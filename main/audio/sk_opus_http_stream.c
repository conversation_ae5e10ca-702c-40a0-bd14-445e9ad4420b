/**
 * @file: sk_opus_http_stream.c
 * @description: 简化的Opus HTTP流播放器
 */
#include "sk_opus_http_stream.h"
#include "sk_opus_dec.h"
#include "sk_os.h"
#include "sk_log.h"
#include "esp_http_client.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/ringbuf.h"
#include <string.h>

static const char *TAG = "OpusHttp";

// 优化配置
#define MIN_BUFFER (32 * 1024)          // 32KB开始播放（降低延迟）
#define RINGBUF_SIZE (256 * 1024)       // 256KB环形缓冲区（增大缓冲）

// 简化的控制结构
typedef struct {
    char url[256];                      // 音频URL
    RingbufHandle_t ringbuf;            // 环形缓冲区
    esp_http_client_handle_t client;    // HTTP客户端
    TaskHandle_t download_task;         // 下载任务
    SemaphoreHandle_t mutex;            // 互斥锁

    volatile bool playing;              // 播放状态
    volatile bool download_done;        // 下载完成
    volatile size_t downloaded_bytes;   // 已下载字节数

    // OGG解析状态
    volatile bool ogg_header_parsed;    // OGG头部是否已解析
    uint8_t *ogg_buffer;                // OGG解析缓冲区（PSRAM）
    size_t ogg_buffer_size;             // OGG缓冲区大小
    size_t ogg_buffer_len;              // OGG缓冲区当前数据长度
} OpusHttpCtrl;

static OpusHttpCtrl g_ctrl = {0};

// OGG数据提取效率统计
static size_t g_total_ogg_input = 0;
static size_t g_total_opus_output = 0;
static size_t g_total_pages_processed = 0;
static size_t g_total_pages_skipped = 0;

// OGG页面解析函数
static size_t parse_ogg_page(const uint8_t *data, size_t len, uint8_t **opus_data, size_t *opus_len) {
    *opus_data = NULL;
    *opus_len = 0;

    if (len < 27) {
        return 0; // 页面头部不完整
    }

    // 检查OGG页面标识 "OggS"
    if (memcmp(data, "OggS", 4) != 0) {
        return 0; // 不是有效的OGG页面
    }

    // 解析页面头部
    uint8_t segments = data[26];  // 段数量
    if (len < 27 + segments) {
        return 0; // 段表不完整
    }

    // 计算页面总大小
    size_t page_size = 27 + segments;
    size_t payload_size = 0;

    for (int i = 0; i < segments; i++) {
        payload_size += data[27 + i];
    }

    if (len < page_size + payload_size) {
        return 0; // 页面数据不完整
    }

    // 检查页面类型
    const uint8_t *payload = data + page_size;

    // 跳过OpusHead和OpusTags页面
    if (payload_size >= 8) {
        if (memcmp(payload, "OpusHead", 8) == 0) {
            g_total_pages_skipped++;
            ESP_LOGI(TAG, "Skipping OpusHead page (%d bytes) - total skipped: %d",
                     payload_size, g_total_pages_skipped);
            return page_size + payload_size;
        }
        if (memcmp(payload, "OpusTags", 8) == 0) {
            g_total_pages_skipped++;
            ESP_LOGI(TAG, "Skipping OpusTags page (%d bytes) - total skipped: %d",
                     payload_size, g_total_pages_skipped);
            return page_size + payload_size;
        }
    }

    // 这是音频数据页面，提取Opus数据
    *opus_data = (uint8_t *)payload;
    *opus_len = payload_size;
    g_total_pages_processed++;

    ESP_LOGI(TAG, "Found Opus audio page: %d bytes (page #%d, total_size=%d)",
             payload_size, g_total_pages_processed, page_size + payload_size);
    return page_size + payload_size;
}

// 处理OGG流数据，提取Opus帧
static size_t process_ogg_data(const uint8_t *input_data, size_t input_len,
                              uint8_t *output_data, size_t output_max_len) {
    size_t output_len = 0;
    size_t pages_found = 0;

    // 将新数据添加到缓冲区
    size_t copy_len = (input_len > g_ctrl.ogg_buffer_size - g_ctrl.ogg_buffer_len) ?
                      (g_ctrl.ogg_buffer_size - g_ctrl.ogg_buffer_len) : input_len;

    if (copy_len > 0 && g_ctrl.ogg_buffer) {
        memcpy(g_ctrl.ogg_buffer + g_ctrl.ogg_buffer_len, input_data, copy_len);
        g_ctrl.ogg_buffer_len += copy_len;
    }

    ESP_LOGI(TAG, "OGG buffer: input=%d, copy=%d, buffered=%d/%d",
             input_len, copy_len, g_ctrl.ogg_buffer_len, g_ctrl.ogg_buffer_size);

    // 解析OGG页面
    while (g_ctrl.ogg_buffer_len > 0) {
        uint8_t *opus_data;
        size_t opus_len;

        size_t page_size = parse_ogg_page(g_ctrl.ogg_buffer, g_ctrl.ogg_buffer_len,
                                         &opus_data, &opus_len);

        if (page_size == 0) {
            // 没有完整的页面，等待更多数据
            break;
        }

        // 如果有Opus数据，复制到输出缓冲区
        if (opus_data && opus_len > 0 && output_len + opus_len <= output_max_len) {
            memcpy(output_data + output_len, opus_data, opus_len);
            output_len += opus_len;
            pages_found++;
            g_ctrl.ogg_header_parsed = true;
            ESP_LOGI(TAG, "Extracted Opus data: %d bytes (page #%d in this batch)",
                     opus_len, pages_found);
        }

        // 移除已处理的页面
        if (page_size < g_ctrl.ogg_buffer_len) {
            memmove(g_ctrl.ogg_buffer, g_ctrl.ogg_buffer + page_size,
                   g_ctrl.ogg_buffer_len - page_size);
            g_ctrl.ogg_buffer_len -= page_size;
        } else {
            g_ctrl.ogg_buffer_len = 0;
        }
    }

    if (pages_found > 0) {
        ESP_LOGI(TAG, "OGG processing result: input=%d, output=%d, pages=%d, efficiency=%.1f%%",
                 input_len, output_len, pages_found, (float)output_len/input_len*100);
    }

    return output_len;
}

// 使用ESP-IDF环形缓冲区简化数据操作
static size_t get_available_data_size(void) {
    if (!g_ctrl.ringbuf) return 0;

    UBaseType_t items_waiting;
    vRingbufferGetInfo(g_ctrl.ringbuf, NULL, NULL, NULL, NULL, &items_waiting);
    return items_waiting;
}

// 从环形缓冲区读取数据
static size_t read_opus_data(uint8_t *data, size_t max_len) {
    if (!g_ctrl.ringbuf || !g_ctrl.playing) {
        return 0;
    }

    size_t item_size;
    uint8_t *item = (uint8_t *)xRingbufferReceive(g_ctrl.ringbuf, &item_size, 0);

    if (item != NULL) {
        size_t copy_size = (item_size > max_len) ? max_len : item_size;
        memcpy(data, item, copy_size);
        vRingbufferReturnItem(g_ctrl.ringbuf, item);
        return copy_size;
    }

    return 0;
}

static bool write_opus_data(const uint8_t *data, size_t len) {
    if (!g_ctrl.ringbuf || len == 0) {
        return false;
    }

    // 动态分配临时缓冲区处理OGG数据
    uint8_t *opus_output = (uint8_t *)malloc(1024);  // 临时缓冲区
    if (!opus_output) {
        ESP_LOGW(TAG, "Failed to allocate temporary buffer");
        return false;
    }
    size_t opus_len = 0;

    // 检查是否是OGG格式
    if (len >= 4 && memcmp(data, "OggS", 4) == 0) {
        // 处理OGG数据，提取Opus帧
        g_total_ogg_input += len;
        opus_len = process_ogg_data(data, len, opus_output, 1024);

        if (opus_len > 0) {
            g_total_opus_output += opus_len;
            float efficiency = (float)g_total_opus_output / g_total_ogg_input * 100;
            ESP_LOGI(TAG, "OGG→Opus conversion: input=%d→%d bytes, total_efficiency=%.1f%% (%d/%d)",
                     len, opus_len, efficiency, g_total_opus_output, g_total_ogg_input);

            // 发送提取的Opus数据到环形缓冲区
            if (xRingbufferSend(g_ctrl.ringbuf, opus_output, opus_len, pdMS_TO_TICKS(10)) == pdTRUE) {
                g_ctrl.downloaded_bytes += len;
                free(opus_output);
                return true;
            }
        } else {
            // 没有提取到Opus数据（可能是头部页面），继续处理
            static int skip_count = 0;
            skip_count++;
            if (skip_count % 10 == 1) {
                ESP_LOGI(TAG, "No Opus data extracted from %d bytes OGG (skip count: %d, total_input: %d)",
                         len, skip_count, g_total_ogg_input);
            }
            g_ctrl.downloaded_bytes += len;
            free(opus_output);
            return true;
        }
    } else {
        // 非OGG数据，直接发送
        if (xRingbufferSend(g_ctrl.ringbuf, data, len, pdMS_TO_TICKS(10)) == pdTRUE) {
            g_ctrl.downloaded_bytes += len;
            free(opus_output);
            return true;
        }
    }

    // 缓冲区满，稍微等待后重试
    vTaskDelay(pdMS_TO_TICKS(5));
    const uint8_t *send_data = (opus_len > 0) ? opus_output : data;
    size_t send_len = (opus_len > 0) ? opus_len : len;

    bool result = false;
    if (xRingbufferSend(g_ctrl.ringbuf, send_data, send_len, pdMS_TO_TICKS(5)) == pdTRUE) {
        g_ctrl.downloaded_bytes += len;
        result = true;
    } else {
        ESP_LOGW(TAG, "Buffer full, dropped %d bytes", len);
        result = false;
    }

    // 释放临时缓冲区
    free(opus_output);
    return result;
}

static esp_err_t http_event_handler(esp_http_client_event_t *evt) {
    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            if (evt->data_len > 0) {
                // 写入环形缓冲区
                if (write_opus_data((uint8_t *)evt->data, evt->data_len)) {
                    // 缓冲足够时开始播放
                    if (!g_ctrl.playing && get_available_data_size() >= MIN_BUFFER) {
                        g_ctrl.playing = true;
                        ESP_LOGI(TAG, "Start playing (buffered: %d KB)",
                                get_available_data_size() / 1024);
                    }
                }
            }
            break;

        case HTTP_EVENT_ON_FINISH:
            g_ctrl.download_done = true;
            ESP_LOGI(TAG, "Download completed: %d bytes", g_ctrl.downloaded_bytes);
            break;

        case HTTP_EVENT_ERROR:
            ESP_LOGE(TAG, "HTTP error");
            break;

        default:
            break;
    }
    return ESP_OK;
}

static void download_task(void *arg) {
    // 简单的HTTP客户端配置
    esp_http_client_config_t config = {
        .url = g_ctrl.url,
        .event_handler = http_event_handler,
        .timeout_ms = 30000,
        .buffer_size = 4096,
    };

    g_ctrl.client = esp_http_client_init(&config);
    if (!g_ctrl.client) {
        ESP_LOGE(TAG, "Failed to init HTTP client");
        g_ctrl.download_task = NULL;
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "Downloading: %s", g_ctrl.url);

    // 执行HTTP请求
    esp_err_t err = esp_http_client_perform(g_ctrl.client);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "HTTP request failed: %s", esp_err_to_name(err));
    }

    // 清理
    esp_http_client_cleanup(g_ctrl.client);
    g_ctrl.client = NULL;
    g_ctrl.download_task = NULL;
    vTaskDelete(NULL);
}

// 优化的初始化函数，使用ESP-IDF环形缓冲区
int32_t SkOpusHttpInit(void) {
    if (g_ctrl.ringbuf != NULL) {
        ESP_LOGW(TAG, "Already initialized");
        return SK_RET_SUCCESS;
    }

    // 清零控制结构
    memset(&g_ctrl, 0, sizeof(g_ctrl));

    // 创建环形缓冲区（使用PSRAM）
    g_ctrl.ringbuf = xRingbufferCreate(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF);
    if (!g_ctrl.ringbuf) {
        ESP_LOGE(TAG, "Failed to create ring buffer");
        return SK_RET_NO_MEMORY;
    }

    // 创建OGG解析缓冲区（使用PSRAM）
    g_ctrl.ogg_buffer_size = 4096;  // 4KB缓冲区
    g_ctrl.ogg_buffer = (uint8_t *)heap_caps_malloc(g_ctrl.ogg_buffer_size, MALLOC_CAP_SPIRAM);
    if (!g_ctrl.ogg_buffer) {
        ESP_LOGW(TAG, "Failed to allocate OGG buffer in PSRAM, using internal RAM");
        g_ctrl.ogg_buffer = (uint8_t *)malloc(g_ctrl.ogg_buffer_size);
        if (!g_ctrl.ogg_buffer) {
            ESP_LOGE(TAG, "Failed to allocate OGG buffer");
            vRingbufferDelete(g_ctrl.ringbuf);
            g_ctrl.ringbuf = NULL;
            return SK_RET_NO_MEMORY;
        }
    }
    g_ctrl.ogg_buffer_len = 0;
    g_ctrl.ogg_header_parsed = false;

    // 创建互斥锁
    g_ctrl.mutex = xSemaphoreCreateMutex();
    if (!g_ctrl.mutex) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = NULL;
        ESP_LOGE(TAG, "Failed to create mutex");
        return SK_RET_FAIL;
    }

    ESP_LOGI(TAG, "Initialized (Ring buffer: %d KB)", RINGBUF_SIZE / 1024);
    return SK_RET_SUCCESS;
}

void SkOpusHttpDeinit(void) {
    // 停止播放
    SkOpusHttpStop();

    // 清理环形缓冲区
    if (g_ctrl.ringbuf) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = NULL;
    }

    // 清理OGG缓冲区
    if (g_ctrl.ogg_buffer) {
        free(g_ctrl.ogg_buffer);
        g_ctrl.ogg_buffer = NULL;
        g_ctrl.ogg_buffer_len = 0;
        g_ctrl.ogg_buffer_size = 0;
    }

    // 清理互斥锁
    if (g_ctrl.mutex) {
        vSemaphoreDelete(g_ctrl.mutex);
        g_ctrl.mutex = NULL;
    }

    // 清零控制结构
    memset(&g_ctrl, 0, sizeof(g_ctrl));
    ESP_LOGI(TAG, "Deinitialized");
}

int32_t SkOpusHttpPlay(const char* url) {
    if (!url || strlen(url) >= sizeof(g_ctrl.url)) {
        ESP_LOGE(TAG, "Invalid URL");
        return SK_RET_INVALID_PARAM;
    }

    if (!g_ctrl.ringbuf) {
        ESP_LOGE(TAG, "Not initialized");
        return SK_RET_NOT_INITIALIZED;
    }

    // 停止当前播放
    SkOpusHttpStop();

    // 重置状态
    strncpy(g_ctrl.url, url, sizeof(g_ctrl.url) - 1);
    g_ctrl.url[sizeof(g_ctrl.url) - 1] = '\0';
    g_ctrl.playing = false;
    g_ctrl.download_done = false;
    g_ctrl.downloaded_bytes = 0;
    g_ctrl.ogg_header_parsed = false;
    g_ctrl.ogg_buffer_len = 0;

    // 清空环形缓冲区（重新创建）
    if (g_ctrl.ringbuf) {
        vRingbufferDelete(g_ctrl.ringbuf);
        g_ctrl.ringbuf = xRingbufferCreate(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF);
    }

    // 创建下载任务
    if (xTaskCreate(download_task, "OpusHttpDL", 8192, NULL, 5, &g_ctrl.download_task) != pdPASS) {
        ESP_LOGE(TAG, "Failed to create download task");
        return SK_RET_FAIL;
    }

    ESP_LOGI(TAG, "Started streaming: %s", url);

    // 添加调试信息
    ESP_LOGI(TAG, "Debug: playing=%d, ringbuf=%p", g_ctrl.playing, g_ctrl.ringbuf);

    return SK_RET_SUCCESS;
}

int32_t SkOpusHttpStop(void) {
    // 停止播放
    g_ctrl.playing = false;

    // 停止HTTP客户端
    if (g_ctrl.client) {
        esp_http_client_close(g_ctrl.client);
    }

    // 停止下载任务
    if (g_ctrl.download_task) {
        vTaskDelete(g_ctrl.download_task);
        g_ctrl.download_task = NULL;
    }

    ESP_LOGI(TAG, "Stopped");
    return SK_RET_SUCCESS;
}

size_t SkOpusHttpFeedAudio(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    static uint32_t call_count = 0;
    call_count++;

    // 每100次调用打印一次调试信息
    if (call_count % 100 == 1) {
        ESP_LOGI(TAG, "Audio callback called %u times, playing=%d, ringbuf=%p",
                call_count, g_ctrl.playing, g_ctrl.ringbuf);
    }

    // 检查播放状态
    if (!g_ctrl.playing || !g_ctrl.ringbuf) {
        memset(data, 0, len);
        return len;
    }

    // 动态分配Opus数据缓冲区
    uint8_t *opus_data = (uint8_t *)malloc(960);
    if (!opus_data) {
        ESP_LOGW(TAG, "Failed to allocate Opus data buffer");
        memset(data, 0, len);
        return len;
    }

    // 从环形缓冲区读取Opus数据
    size_t read_len = read_opus_data(opus_data, 960);

    if (read_len > 0) {
        // 为Opus数据添加4字节头部（兼容现有解码器）
        if (read_len <= 960) {
            // 动态分配缓冲区，避免栈溢出
            uint8_t *opus_with_header = (uint8_t *)malloc(read_len + 4);
            if (opus_with_header) {
                // 添加4字节头部
                opus_with_header[0] = 0x00;
                opus_with_header[1] = 0x00;
                opus_with_header[2] = 0x00;
                opus_with_header[3] = 0x00;
                memcpy(opus_with_header + 4, opus_data, read_len);

                // 送入Opus解码器
                SkAudioDownlinkTimeRecord record = {0};
                int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0, opus_with_header, read_len + 4, &record);

                if (ret != SK_RET_SUCCESS) {
                    if (call_count % 50 == 1) {
                        size_t available = get_available_data_size();
                        ESP_LOGI(TAG, "Opus decode failed: %d (len=%d, buffer_available=%d)",
                                 ret, read_len + 4, available);
                    }
                } else {
                    if (call_count % 50 == 1) {
                        size_t available = get_available_data_size();
                        ESP_LOGI(TAG, "Opus data sent to decoder: %d bytes (buffer_available=%d, total_pages=%d)",
                                 read_len + 4, available, g_total_pages_processed);
                    }
                }

                // 释放动态分配的内存
                free(opus_with_header);
            } else {
                ESP_LOGW(TAG, "Failed to allocate memory for Opus header");
            }
        }
    }

    // 释放Opus数据缓冲区
    free(opus_data);

    // 如果没有读取到数据，检查是否下载完成
    if (read_len == 0) {
        size_t available = get_available_data_size();
        if (call_count % 50 == 1) {
            ESP_LOGI(TAG, "No data read - download_done:%d, buffer_available:%d bytes",
                     g_ctrl.download_done, available);
        }

        if (g_ctrl.download_done && available == 0) {
            float total_efficiency = g_total_ogg_input > 0 ?
                (float)g_total_opus_output / g_total_ogg_input * 100 : 0;
            ESP_LOGI(TAG, "Playback finished - Stats: downloaded=%d, ogg_input=%d, opus_output=%d, efficiency=%.1f%%, pages_processed=%d, pages_skipped=%d",
                     g_ctrl.downloaded_bytes, g_total_ogg_input, g_total_opus_output,
                     total_efficiency, g_total_pages_processed, g_total_pages_skipped);
            g_ctrl.playing = false;
        }
    }

    // 从Opus解码器获取PCM数据
    size_t pcm_len = SkOpusDecFeedPlayAudio(data, len, timeRecord);
    if (pcm_len > 0) {
        if (call_count % 50 == 1) {
            ESP_LOGI(TAG, "Got PCM data: %d bytes", pcm_len);
        }
    } else {
        if (call_count % 100 == 1) {
            ESP_LOGI(TAG, "No PCM data available");
            // 显示解码器统计信息
            SkOpusDecStat();
        }
    }
    return pcm_len;
}
