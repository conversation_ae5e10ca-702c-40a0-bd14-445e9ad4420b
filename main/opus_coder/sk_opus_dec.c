/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus_dec.c
 * @description: OPUS解码模块
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <arpa/inet.h>
#include "opus.h"
#include "audio_data_configstart.h"
#include "audio_data_configend.h"
#include "audio_data_0.h"
#include "audio_data_1.h"
#include "audio_data_2.h"
#include "audio_data_3.h"
#include "audio_data_4.h"
#include "audio_data_5.h"
#include "audio_data_6.h"
#include "audio_data_7.h"
#include "audio_data_8.h"
#include "audio_data_9.h"
#include "audio_data_point.h"
#include "audio_data_connected.h"
#include "audio_data_reboot.h"
#include "audio_data_onhook.h"
#include "audio_data_wait.h"
#include "audio_data_linkok.h"
#include "audio_data_connecting.h"
#include "audio_data_ctrlconnected.h"
#include "audio_data_needconfig.h"
#include "audio_data_here.h"
#include "audio_data_voiceto.h"
#include "audio_data_10.h"
#include "audio_data_max.h"
#include "audio_data_min.h"
#include "audio_data_serverdisconnected.h"
#include "audio_data_siu.h"
#include "audio_data_enterpm.h"
#include "audio_data_done.h"
#include "audio_data_ok.h"
#include "sk_audio_buffer.h"
#include "sk_opus_dec.h"
#include "sk_common.h"
#include "sk_opus_inner.h"
#include "sk_sm.h"
#include "sk_dfx.h"
#include "sk_os.h"

#define TAG "SkOpusDec"

#define SAMPLE_MODE_8K 0
#define SAMPLE_MODE_16K 1

#define AUDIO_SHORT_BUF_SIZE 256
#define AUDIO_LONG_BUF_SIZE 1960


typedef struct {
    struct OpusDecoder* audioDec;
    QueueHandle_t msgQueue;
    int frameSize;
    int taskStatus;
    void *inQueue;
    void *outQueue;
    uint32_t nextWorkId;
    uint8_t isMute;
    uint8_t loopback;
    uint8_t playOpusIndex;
    uint32_t localEndFlag;
    int32_t audioCnt;
    int32_t audioListIndex;
    uint8_t audioListCnt;
    uint8_t audioList[32];
    uint32_t workId;
    uint8_t newAudioList[32];
    int32_t newAudioListCnt;
    uint32_t newWorkId;

    bool decRemoteEnable;
    uint32_t dtxCnt;
    uint32_t notDtxCnt;

    uint32_t audioMsgCnt;
    uint32_t audioShortCnt;
    uint32_t audioLongCnt;
    uint32_t audioPlayFailCnt;
    uint32_t audioPlayCnt;
    uint32_t audioEqCnt;
    uint32_t audioLocalCnt;
    uint32_t audioLocalTryCnt;
    uint32_t localReq;
    uint32_t remoteReq;
    SkCodedDataItem audioInfoList[LOCAL_PLAY_MAX_CNT];
} SkOpusDecCtrl;

static int32_t SkOpusDecSendEvent(SkOpusDecCtrl *ctrl, uint8_t event);

SkOpusDecCtrl g_opusDecCtrl;

/**
 * @brief 解码 Opus 音频数据到 PCM 数据
 *
 * 使用 Opus 解码器将 Opus 音频数据解码为 PCM 数据。
 *
 * @param handler Opus 解码器的控制句柄
 * @param opusData Opus 音频数据指针
 * @param opusSize Opus 音频数据大小（字节）
 * @param pcmBuffer PCM 数据缓冲区指针
 * @param pcmSize PCM 数据缓冲区大小（字节）
 *
 * @return 解码后的 PCM 数据长度（样本数），如果解码失败则返回 0
 */
int32_t SkOpusDecDecode(SkOpusDecHandler handler, uint8_t* opusData, int opusSize, int16_t* pcmBuffer, int32_t pcmSize) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    int ret;
    
    if (ctrl->isMute == 0) {
        if (opusSize < 16) {
            ctrl->dtxCnt++;
            ctrl->notDtxCnt = 0;
            memset(pcmBuffer, 0, pcmSize * sizeof(int16_t));
            ret = pcmSize;
        } else {
            ctrl->dtxCnt = 0;
            ctrl->notDtxCnt++;
            ret = opus_decode(ctrl->audioDec, opusData, opusSize, pcmBuffer, pcmSize, 0);
        }
    } else {
        ctrl->dtxCnt++;
        memset(pcmBuffer, 0, pcmSize * sizeof(int16_t));
        ret = pcmSize;
    }

    if (ret < 0) {
        ESP_LOGE(TAG, "Failed to decode audio, error code: %d", ret);
        return 0;
    }

    return ret;
}

void SkOpusDecStop() {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    if (ctrl->audioDec != NULL) {
        opus_decoder_ctl(ctrl->audioDec, OPUS_RESET_STATE);
    }
}

uint8_t* SkOpusDecLocalGetPkt(SkOpusDecCtrl *ctrl, uint16_t *pktSize) {
    uint8_t audioIndex;
    SkCodedDataItem *item = NULL;
    SkOpusPktHdr *pktHdr;
    uint8_t *pktData;
    uint16_t pktOffset;

    if (ctrl->audioListIndex >= 32 || ctrl->audioListIndex >= ctrl->audioListCnt) {
        ESP_LOGE(TAG, "Invalid audio index: %d", ctrl->audioListIndex);
        ctrl->localEndFlag = 1;
        return NULL;
    }

    audioIndex = ctrl->audioList[ctrl->audioListIndex];
    if (audioIndex >= ctrl->audioCnt || audioIndex > LOCAL_PLAY_MAX_CNT) {
        ESP_LOGE(TAG, "Invalid audio index: %d", audioIndex);
        ctrl->localEndFlag = 1;
        return NULL;
    }

    item = &ctrl->audioInfoList[audioIndex];
    if (ctrl->playOpusIndex >= item->pktCnt) {
        ESP_LOGE(TAG, "Invalid packet index: %d", ctrl->playOpusIndex);
        ctrl->localEndFlag = 1;
        return NULL;
    }
    pktOffset = item->pktOffsetList[ctrl->playOpusIndex];
    if (pktOffset >= item->length) {
        ESP_LOGE(TAG, "Invalid packet offset: %d", pktOffset);
        ctrl->localEndFlag = 1;
        return NULL;
    }
    pktHdr = (SkOpusPktHdr *)(item->data + pktOffset);
    if (pktHdr->type != OPUS_PKT_FLAG) {
        ESP_LOGE(TAG, "Invalid packet type: %d", pktHdr->type);
        ctrl->localEndFlag = 1;
        return NULL;
    }
    *pktSize = ntohs(pktHdr->payloadSize);
    pktData = (uint8_t *)pktHdr->payload;
    if (ctrl->playOpusIndex + 1 < item->pktCnt) {
        ctrl->playOpusIndex++;
    } else {
        ctrl->playOpusIndex = 0;
        if (ctrl->audioListIndex + 1 < ctrl->audioListCnt) {
            ctrl->audioListIndex++;
        } else {
            ctrl->audioListIndex = 0;
            ctrl->localEndFlag = 1;
        }
    }
    //ESP_LOGI(TAG, "Curr audio %p len %d byte, next audio %d packet: %d", 
    //    pktData, *pktSize, ctrl->audioListIndex, ctrl->playOpusIndex);

    return pktData;
}

void SkOpusDecStartLocalPlay(SkOpusDecHandler handler) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;

    ctrl->audioListIndex = 0;
    ctrl->playOpusIndex = 0;
    ctrl->audioListCnt = ctrl->newAudioListCnt;
    ctrl->workId = ctrl->newWorkId;
    ctrl->localEndFlag = 0;
    memcpy(ctrl->audioList, ctrl->newAudioList, sizeof(uint8_t) * ctrl->newAudioListCnt);
    ESP_LOGD(TAG, "Dec local audio start work %d.", ctrl->workId);

    return;
}

bool SkOpusDecLocalData(SkOpusDecHandler handler) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    uint16_t pktSize;
    uint8_t *pktData = NULL;
    uint16_t sampleCnt;
    SkAudioBuf *audioBuf = NULL;

    if (SkAudioBufferHasFree(ctrl->outQueue)) {
        ctrl->audioLocalTryCnt++;
        audioBuf = SkAudioBufferGetFree(ctrl->outQueue, 0);
        if (audioBuf == NULL) {
            ESP_LOGE(TAG, "Failed to get free audio buffer");
            return false;
        }
        pktData = SkOpusDecLocalGetPkt(ctrl, &pktSize);
        if (pktData == NULL) {
            ESP_LOGE(TAG, "Failed to get packet data");
            SkAudioBufferPutFree(ctrl->outQueue, audioBuf);
            return false;
        }
        sampleCnt = SkOpusDecDecode((SkOpusDecHandler)ctrl, pktData, pktSize, (int16_t *)audioBuf->data, ctrl->frameSize);
        audioBuf->length = sampleCnt * sizeof(int16_t);
        if (ctrl->localEndFlag == 1) {
            audioBuf->voiceType = 1;
            audioBuf->dataFlag = ctrl->workId;
        } else {
            audioBuf->voiceType = 0;
        }
        SkAudioBufferPutData(ctrl->outQueue, audioBuf);
        ctrl->audioLocalCnt++;
        //ESP_LOGI(TAG, "Play audio data %d to %d sample into queue ret %d.", pktSize, sampleCnt, ret);
    }

    if (ctrl->localEndFlag == 1) {
        ESP_LOGD(TAG, "Dec local audio finish work %d, last size is %d.", ctrl->workId, pktSize);
        return false;
    }

    return SkAudioBufferHasFree(ctrl->outQueue);
}

void SkOpusDecFreeInqueueBuffer(SkOpusDecCtrl *ctrl, SkAudioBuf *audioBuf) {
    int32_t ret;

    ctrl->audioShortCnt++;
    ret = SkAudioBufferPutFree(ctrl->inQueue, audioBuf);
    if (ret != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "Failed to put free inQueue ret %d", ret);
    }
    return;
}

void SkOpusDecPutDataIntoQueue(SkOpusDecHandler handler, SkAudioBuf *inBuf) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    
    if (inBuf == NULL) {
        return;
    }
    SkAudioBufferPutData(ctrl->inQueue, inBuf);
    return;
}

static void SkOpusDecRemoteData(SkOpusDecHandler handler, SkAudioBuf *inBuf, SkAudioBuf *outBuf) {
    uint16_t sampleCnt = 0;
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)outBuf->timeRecord;

    if (inBuf->length == 0) {
        outBuf->length = 0;
        outBuf->sessionId = inBuf->sessionId;
        return;
    }

    ctrl->audioMsgCnt++;
    sampleCnt = SkOpusDecDecode((SkOpusDecHandler)ctrl, inBuf->data + 4, inBuf->length - 4,
        (int16_t *)outBuf->data, ctrl->frameSize);
    ESP_LOGI(TAG, "Opus decode result: input=%d, output_samples=%d", 
        inBuf->length, sampleCnt);
    outBuf->length = sampleCnt * sizeof(int16_t);
    memcpy(&outBuf->timeRecord, &inBuf->timeRecord, sizeof(inBuf->timeRecord));
    timeRecord->decDoneTick = SkOsGetTickCnt();
    
    return;
}

bool SkOpusDecRemote(SkOpusDecHandler handler) {
    SkAudioBuf *inBuf = NULL;
    SkAudioBuf *outBuf = NULL;
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;

    if ((!SkAudioBufferHasData(ctrl->inQueue)) || (!SkAudioBufferHasFree(ctrl->outQueue))) {
        return false;
    }

    outBuf = SkAudioBufferGetFree(ctrl->outQueue, 0);
    if (outBuf == NULL) {
        ESP_LOGE(TAG, "Failed to get free audio buffer");
        return false;
    }

    inBuf = SkAudioBufferGetData(ctrl->inQueue, 0);
    if (inBuf == NULL) {
        SkAudioBufferPutFree(ctrl->outQueue, outBuf);
        return false;
    }

    SkOpusDecRemoteData((SkOpusDecHandler)ctrl, inBuf, outBuf);
    SkOpusDecFreeInqueueBuffer(ctrl, inBuf);
    outBuf->voiceType = 0;
    if (SkAudioBufferPutData(ctrl->outQueue, outBuf) != SK_RET_SUCCESS) {
        ctrl->audioPlayFailCnt++;
        SkAudioBufferPutFree(ctrl->outQueue, outBuf);
    } else {
        ctrl->audioEqCnt++;
    }

    return SkAudioBufferHasData(ctrl->inQueue) && SkAudioBufferHasFree(ctrl->outQueue);
}

uint32_t SkOpusDecPlayLocal(uint8_t *audioList, int32_t len) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    SkOpusMsg msg;

    if (len > 32) {
        return 0;
    }
    memcpy(ctrl->newAudioList, audioList, len);
    ctrl->newAudioListCnt = len;
    ctrl->newWorkId = ctrl->nextWorkId;
    ctrl->nextWorkId++;

    msg.event = SK_OPUS_EVENT_PLAY_LOCAL;
    xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY);
    ctrl->localReq++;

    return ctrl->newWorkId;
}

void SkOpusDecRegAudio(char* name, const uint8_t* data, int length, const uint16_t *pktOffset, int16_t pktCnt) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    if (ctrl->audioCnt >= LOCAL_PLAY_MAX_CNT) {
        return;
    }
    ctrl->audioInfoList[ctrl->audioCnt].name = name;
    ctrl->audioInfoList[ctrl->audioCnt].data = data;
    ctrl->audioInfoList[ctrl->audioCnt].length = length;
    ctrl->audioInfoList[ctrl->audioCnt].pktOffsetList = pktOffset;
    ctrl->audioInfoList[ctrl->audioCnt].pktCnt = pktCnt;
    ctrl->audioCnt++;

    return;
}

SkOpusDecHandler SkOpusDecInit(int sampleRate, int channels, int durationMs, QueueHandle_t msgQueue) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    int error;

    ctrl->frameSize = sampleRate / 1000 * channels * durationMs;
    ctrl->audioDec = opus_decoder_create(sampleRate, channels, &error);
    ctrl->inQueue = SkCreateAudioQueue(32, AUDIO_SHORT_BUF_SIZE, 0);
    ctrl->outQueue = SkCreateAudioQueue(8, ctrl->frameSize * sizeof(uint16_t), 0);
    ctrl->msgQueue = msgQueue;
    ctrl->loopback = 0;
    ctrl->taskStatus = 1;
    ctrl->dtxCnt = 0;
    ctrl->notDtxCnt = 0;
    ctrl->audioListCnt = 0;
    ctrl->localEndFlag = 1;
    SkOpusDecRegAudio("0", g_audioData0, sizeof(g_audioData0), 
        g_audioInfo0, sizeof(g_audioInfo0)/sizeof(uint16_t));
    SkOpusDecRegAudio("1", g_audioData1, sizeof(g_audioData1), 
        g_audioInfo1, sizeof(g_audioInfo1)/sizeof(uint16_t));
    SkOpusDecRegAudio("2", g_audioData2, sizeof(g_audioData2), 
        g_audioInfo2, sizeof(g_audioInfo2)/sizeof(uint16_t));
    SkOpusDecRegAudio("3", g_audioData3, sizeof(g_audioData3), 
        g_audioInfo3, sizeof(g_audioInfo3)/sizeof(uint16_t));
    SkOpusDecRegAudio("4", g_audioData4, sizeof(g_audioData4), 
        g_audioInfo4, sizeof(g_audioInfo4)/sizeof(uint16_t));
    SkOpusDecRegAudio("5", g_audioData5, sizeof(g_audioData5), 
        g_audioInfo5, sizeof(g_audioInfo5)/sizeof(uint16_t));
    SkOpusDecRegAudio("6", g_audioData6, sizeof(g_audioData6), 
        g_audioInfo6, sizeof(g_audioInfo6)/sizeof(uint16_t));
    SkOpusDecRegAudio("7", g_audioData7, sizeof(g_audioData7), 
        g_audioInfo7, sizeof(g_audioInfo7)/sizeof(uint16_t));
    SkOpusDecRegAudio("8", g_audioData8, sizeof(g_audioData8), 
        g_audioInfo8, sizeof(g_audioInfo8)/sizeof(uint16_t));
    SkOpusDecRegAudio("9", g_audioData9, sizeof(g_audioData9), 
        g_audioInfo9, sizeof(g_audioInfo9)/sizeof(uint16_t));
    SkOpusDecRegAudio("Point", g_audioDataPoint, sizeof(g_audioDataPoint), 
        g_audioInfoPoint, sizeof(g_audioInfoPoint)/sizeof(uint16_t));
    SkOpusDecRegAudio("ConfigStart", g_audioDataConfigStart, sizeof(g_audioDataConfigStart), 
        g_audioInfoConfigStart, sizeof(g_audioInfoConfigStart)/sizeof(uint16_t));
    SkOpusDecRegAudio("ConfigEnd", g_audioDataConfigEnd, sizeof(g_audioDataConfigEnd), 
        g_audioInfoConfigEnd, sizeof(g_audioInfoConfigEnd)/sizeof(uint16_t));
    SkOpusDecRegAudio("Reboot", g_audioDataReboot, sizeof(g_audioDataReboot), 
        g_audioInfoReboot, sizeof(g_audioInfoReboot)/sizeof(uint16_t));
    SkOpusDecRegAudio("Connected", g_audioDataConnected, sizeof(g_audioDataConnected), 
        g_audioInfoConnected, sizeof(g_audioInfoConnected)/sizeof(uint16_t));
    SkOpusDecRegAudio("Onhook", g_audioDataOnhook, sizeof(g_audioDataOnhook), 
        g_audioInfoOnhook, sizeof(g_audioInfoOnhook)/sizeof(uint16_t));
    SkOpusDecRegAudio("Wait", g_audioDataWait, sizeof(g_audioDataWait), 
        g_audioInfoWait, sizeof(g_audioInfoWait)/sizeof(uint16_t));
    SkOpusDecRegAudio("CLinkOk", g_audioDataCtrlConnected, sizeof(g_audioDataCtrlConnected), 
        g_audioInfoCtrlConnected, sizeof(g_audioInfoCtrlConnected)/sizeof(uint16_t));
    SkOpusDecRegAudio("Start", g_audioDataLinkOk, sizeof(g_audioDataLinkOk), 
        g_audioInfoLinkOk, sizeof(g_audioInfoLinkOk)/sizeof(uint16_t));
    SkOpusDecRegAudio("NeedCfg", g_audioDataNeedConfig, sizeof(g_audioDataNeedConfig), 
        g_audioInfoNeedConfig, sizeof(g_audioInfoNeedConfig)/sizeof(uint16_t));
    SkOpusDecRegAudio("Connecting", g_audioDataConnecting, sizeof(g_audioDataConnecting), 
        g_audioInfoConnecting, sizeof(g_audioInfoConnecting)/sizeof(uint16_t));
    SkOpusDecRegAudio("Here", g_audioDataHere, sizeof(g_audioDataHere), 
        g_audioInfoHere, sizeof(g_audioInfoHere)/sizeof(uint16_t));
    SkOpusDecRegAudio("VoiceTo", g_audioDataVoiceTo, sizeof(g_audioDataVoiceTo), 
        g_audioInfoVoiceTo, sizeof(g_audioInfoVoiceTo)/sizeof(uint16_t));
    SkOpusDecRegAudio("Ten", g_audioData10, sizeof(g_audioData10), 
        g_audioInfo10, sizeof(g_audioInfo10)/sizeof(uint16_t));
    SkOpusDecRegAudio("Max", g_audioDataMax, sizeof(g_audioDataMax), 
        g_audioInfoMax, sizeof(g_audioInfoMax)/sizeof(uint16_t));
    SkOpusDecRegAudio("Min", g_audioDataMin, sizeof(g_audioDataMin), 
        g_audioInfoMin, sizeof(g_audioInfoMin)/sizeof(uint16_t));
    SkOpusDecRegAudio("LostConnect", g_audioDataServerDisconnected, sizeof(g_audioDataServerDisconnected), 
        g_audioInfoServerDisconnected, sizeof(g_audioInfoServerDisconnected)/sizeof(uint16_t));
    SkOpusDecRegAudio("LostConnect", g_audioDataSiu, sizeof(g_audioDataSiu), 
        g_audioInfoSiu, sizeof(g_audioInfoSiu)/sizeof(uint16_t));
    SkOpusDecRegAudio("LostConnect", g_audioDataEnterPm, sizeof(g_audioDataEnterPm), 
        g_audioInfoEnterPm, sizeof(g_audioInfoEnterPm)/sizeof(uint16_t));
    SkOpusDecRegAudio("LostConnect", g_audioDataDone, sizeof(g_audioDataDone), 
        g_audioInfoDone, sizeof(g_audioInfoDone)/sizeof(uint16_t));
    SkOpusDecRegAudio("LostConnect", g_audioDataOK, sizeof(g_audioDataOK), 
        g_audioInfoOK, sizeof(g_audioInfoOK)/sizeof(uint16_t));

    if (ctrl->audioDec == NULL) {
        ESP_LOGE(TAG, "Failed to create audio decoder, error code: %d", error);
        return NULL;
    }

    return ctrl;
}

size_t SkOpusDecFeedPlayAudio(uint16_t *buff, size_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    size_t outLen = len;
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    SkAudioBuf *audioBuf = NULL;

    audioBuf = SkAudioBufferGetData(ctrl->outQueue, 100);
    if (audioBuf == NULL) {
        return 0;
    }

    if (audioBuf->voiceType == 1) {
        ESP_LOGD(TAG, "Play local audio finish.");
        SkSmOnWorkFinish(audioBuf->dataFlag);
    }

    if (audioBuf->length == 0) {
        SkSmOnSessionDecFinish(audioBuf->sessionId);
        SkAudioBufferPutFree(ctrl->outQueue, audioBuf);
        if (timeRecord != NULL) {
            memset(timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
            timeRecord->playTick = SkOsGetTickCnt();
        }
    } else {
        ctrl->audioPlayCnt++;
        if (audioBuf->length < len) {
            outLen = audioBuf->length;
        }
        memcpy(buff, audioBuf->data, outLen);
        if (timeRecord != NULL) {
            memcpy(timeRecord, &audioBuf->timeRecord, sizeof(SkAudioDownlinkTimeRecord));
            timeRecord->playTick = SkOsGetTickCnt();
        }
        SkAudioBufferPutFree(ctrl->outQueue, audioBuf);
        SkOpusDecSendEvent(ctrl, SK_OPUS_EVENT_DEC_OUTPUT);
    }
    
    return outLen;
}


void SkOpusDecDeinit(SkOpusDecHandler handler) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    if (ctrl->audioDec != NULL) {
        opus_decoder_destroy(ctrl->audioDec);
        ctrl->audioDec = NULL;
    }
    return;
}

SkOpusDecHandler SkOpusDecGetHandler() {
    return &g_opusDecCtrl;
}

int32_t SkOpusDecPlayRemote(SkOpusDecHandler handler, uint16_t sessionId, 
    uint8_t *data, int32_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    SkAudioBuf *audioBuf = NULL;
    SkOpusMsg msg;

    if (!ctrl->decRemoteEnable) {
        return SK_RET_SUCCESS;
    }

    if (ctrl->localEndFlag == 0) {
        return SK_RET_SUCCESS;
    }

    if (data == NULL || len <= 0) {
        return SK_RET_INVALID_PARAM;
    }

    if (len > AUDIO_SHORT_BUF_SIZE) {
        return SK_RET_INVALID_PARAM;
    }

    audioBuf = SkAudioBufferGetFree(ctrl->inQueue, 0);
    if (audioBuf == NULL) {
        return SK_RET_NO_MEMORY;
    }
    memcpy(audioBuf->data, data, len);
    if (timeRecord != NULL) {
        memcpy(&audioBuf->timeRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    }
    audioBuf->sessionId = sessionId;
    audioBuf->length = len;
    msg.event = SK_OPUS_EVENT_REMOTE_SHORT;
    msg.arg = audioBuf;
    if (xQueueSend(ctrl->msgQueue, &msg, 0) != pdPASS) {
        SkAudioBufferPutFree(ctrl->inQueue, audioBuf);
        return SK_RET_FAIL;
    }
    ctrl->remoteReq++;
    return SK_RET_SUCCESS;
}

void SkOpusDecRemoteDataEnd(SkOpusDecHandler handler, uint16_t sessionId) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    SkOpusMsg msg;
    SkAudioBuf *audioBuf = NULL;

    audioBuf = SkAudioBufferGetFree(ctrl->inQueue, 0);
    msg.event = SK_OPUS_EVENT_REMOTE_SHORT;
    if (audioBuf == NULL) {
        return;
    }
    msg.arg = audioBuf;
    audioBuf->length = 0;
    audioBuf->sessionId = sessionId;
    if (xQueueSend(ctrl->msgQueue, &msg, 0) != pdPASS) {
        SkAudioBufferPutFree(ctrl->inQueue, audioBuf);
        return;
    }
    ctrl->remoteReq++;
    return;
}

static int32_t SkOpusDecSendEvent(SkOpusDecCtrl *ctrl, uint8_t event) {
    SkOpusMsg msg;

    msg.event = event;
    if (xQueueSend(ctrl->msgQueue, &msg, 0) != pdPASS) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int32_t SkOpusDecRemoteEnd() {
    return SkOpusDecSendEvent(&g_opusDecCtrl, SK_OPUS_EVENT_REMOTE_END);
}


int32_t SkOpusDecMuteRemote() {
    return SkOpusDecSendEvent(&g_opusDecCtrl, SK_OPUS_EVENT_MUTE_REMOTE);
}

int32_t SkOpusDecUnmuteRemote() {
    return SkOpusDecSendEvent(&g_opusDecCtrl, SK_OPUS_EVENT_UNMUTE_REMOTE);
}

void SkOpusDecStat() {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;

    ESP_LOGI(TAG, " DEC: RxCnt=%u, RxShortCnt=%u, RxLongCnt=%u, PlayFailCnt=%u, PlayCnt=%u, EqCnt=%u, LTry=%u, LCnt=%u, lReq=%u, rReq=%u",
        ctrl->audioMsgCnt, ctrl->audioShortCnt, ctrl->audioLongCnt,
        ctrl->audioPlayFailCnt, ctrl->audioPlayCnt, ctrl->audioEqCnt,
        ctrl->audioLocalTryCnt, ctrl->audioLocalCnt, ctrl->localReq, ctrl->remoteReq);

    return;
}

void SkOpusDecMute(uint8_t muteFlag) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    ctrl->isMute = muteFlag;
}

void SkOpusDecSetLoopback(uint8_t loopback) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    ctrl->loopback = loopback;
}

int32_t SkOpusDecGetPlayState() {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    if (ctrl->notDtxCnt <= 2) {
        return SK_OPUS_STATE_DTX;
    } else {
        return SK_OPUS_STATE_SPEECH;
    }
}

void SkOpusDecDisplayQueueInfo() {
    AudioQueueDisplay(g_opusDecCtrl.inQueue);
    AudioQueueDisplay(g_opusDecCtrl.outQueue);
    return;
}

void SkOpusDecEnableRemote(uint8_t enable) {
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    ctrl->decRemoteEnable = enable;
}

bool SkOpusDecProc(SkOpusDecHandler handler) {
    bool dataContinue;
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;

    if (ctrl->localEndFlag == 0) {
        dataContinue = SkOpusDecLocalData(ctrl);
        return dataContinue;
    }

    return SkOpusDecRemote(handler);
}