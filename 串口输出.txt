E (23264) SmTop: Command 3
I (23264) SmTop: State start 7
I (23264) SmMusic: Start music (HTTP Opus mode)
I (23264) SmMusic: Opus decoder remote enabled
I (23264) SkOpusDec:  DEC: RxCnt=0, RxShortCnt=0, RxLongCnt=0, PlayFailCnt=0, PlayCnt=175, EqCnt=0, LTry=175, LCnt=175, lReq=2, rReq=0
I (23274) AudioBuf: index 0 status 0 session 0 data 0x3c1cdabc size 256 offset 0
I (23284) AudioBuf: index 1 status 0 session 0 data 0x3c1cdbc0 size 256 offset 0
I (23284) AudioBuf: index 2 status 0 session 0 data 0x3c1cdcc4 size 256 offset 0
I (23294) AudioBuf: index 3 status 0 session 0 data 0x3c1cddc8 size 256 offset 0
I (23304) AudioBuf: index 4 status 0 session 0 data 0x3c1cdecc size 256 offset 0
I (23314) AudioBuf: index 5 status 0 session 0 data 0x3c1cdfd0 size 256 offset 0
I (23314) AudioBuf: index 6 status 0 session 0 data 0x3c1ce0d4 size 256 offset 0
I (23324) AudioBuf: index 7 status 0 session 0 data 0x3c1ce1d8 size 256 offset 0
I (23334) AudioBuf: index 8 status 0 session 0 data 0x3c1ce2dc size 256 offset 0
I (23334) AudioBuf: index 9 status 0 session 0 data 0x3c1ce3e0 size 256 offset 0
I (23344) AudioBuf: index 10 status 0 session 0 data 0x3c1ce4e4 size 256 offset 0
I (23354) AudioBuf: index 11 status 0 session 0 data 0x3c1ce5e8 size 256 offset 0
I (23364) AudioBuf: index 12 status 0 session 0 data 0x3c1ce6ec size 256 offset 0
I (23364) AudioBuf: index 13 status 0 session 0 data 0x3c1ce7f0 size 256 offset 0
I (23374) AudioBuf: index 14 status 0 session 0 data 0x3c1ce8f4 size 256 offset 0
I (23384) AudioBuf: index 15 status 0 session 0 data 0x3c1ce9f8 size 256 offset 0
I (23384) AudioBuf: index 16 status 0 session 0 data 0x3c1ceafc size 256 offset 0
I (23394) AudioBuf: index 17 status 0 session 0 data 0x3c1cec00 size 256 offset 0
I (23404) AudioBuf: index 18 status 0 session 0 data 0x3c1ced04 size 256 offset 0
I (23414) AudioBuf: index 19 status 0 session 0 data 0x3c1cee08 size 256 offset 0
I (23414) AudioBuf: index 20 status 0 session 0 data 0x3c1cef0c size 256 offset 0
I (23424) AudioBuf: index 21 status 0 session 0 data 0x3c1cf010 size 256 offset 0
I (23434) AudioBuf: index 22 status 0 session 0 data 0x3c1cf114 size 256 offset 0
I (23434) AudioBuf: index 23 status 0 session 0 data 0x3c1cf218 size 256 offset 0
I (23444) AudioBuf: index 24 status 0 session 0 data 0x3c1cf31c size 256 offset 0
I (23454) AudioBuf: index 25 status 0 session 0 data 0x3c1cf420 size 256 offset 0
I (23464) AudioBuf: index 26 status 0 session 0 data 0x3c1cf524 size 256 offset 0
I (23464) AudioBuf: index 27 status 0 session 0 data 0x3c1cf628 size 256 offset 0
I (23474) AudioBuf: index 28 status 0 session 0 data 0x3c1cf72c size 256 offset 0
I (23484) AudioBuf: index 29 status 0 session 0 data 0x3c1cf830 size 256 offset 0
I (23494) AudioBuf: index 30 status 0 session 0 data 0x3c1cf934 size 256 offset 0
I (23494) AudioBuf: index 31 status 0 session 0 data 0x3c1cfa38 size 256 offset 0
I (23504) AudioBuf: index 0 status 0 session 0 data 0x3c1cfb3c size 1920 offset 0
I (23514) AudioBuf: index 1 status 0 session 0 data 0x3c1d02c0 size 1920 offset 0
I (23514) AudioBuf: index 2 status 0 session 0 data 0x3c1d0a44 size 1920 offset 0
I (23524) AudioBuf: index 3 status 0 session 0 data 0x3c1d11c8 size 1920 offset 0
I (23534) AudioBuf: index 4 status 0 session 0 data 0x3c1d194c size 1920 offset 0
I (23544) AudioBuf: index 5 status 0 session 0 data 0x3c1d20d0 size 1920 offset 0
I (23544) AudioBuf: index 6 status 0 session 0 data 0x3c1d2854 size 1920 offset 0
I (23554) AudioBuf: index 7 status 0 session 0 data 0x3c1d2fd8 size 1920 offset 0
I (23564) OpusHttp: Stopped
I (23564) OpusHttp: Started streaming: http://************:8070/Desktop/music.opus
I (23574) OpusHttp: Debug: playing=0, ringbuf=0x3fcd2b28
I (23564) OpusHttp: Downloading: http://************:8070/Desktop/music.opus
I (23574) SmMusic: HTTP Opus stream started
I (23574) main_task: Returned from app_main()
I (23594) OpusHttp: Audio callback called 1 times, playing=0, ringbuf=0x3fcd2b28
I (23614) OpusHttp: OGG buffer: input=1440, copy=1440, buffered=1440/16384
I (23614) OpusHttp: Skipping OpusHead page (19 bytes) - total skipped: 1
I (23614) OpusHttp: Skipping OpusTags page (61 bytes) - total skipped: 2
I (23624) OpusHttp: No Opus data extracted from 1440 bytes (skip count: 1, total_input: 1440, buffered: 1304)
I (23634) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5400/16384
I (23644) OpusHttp: Found Opus audio page: 3311 bytes (page #1, total_size=3357)
I (23644) OpusHttp: Extracted Opus data: 3311 bytes (page #1 in this batch)
I (23654) OpusHttp: OGG processing result: input=4096, output=3311, pages=1, efficiency=80.8%
I (23664) OpusHttp: OGG→Opus conversion: input=4096→3311 bytes, total_efficiency=59.8% (3311/5536)
I (23674) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6139/16384
I (23674) OpusHttp: Found Opus audio page: 4016 bytes (page #2, total_size=4060)
I (23684) OpusHttp: Extracted Opus data: 4016 bytes (page #1 in this batch)
I (23694) OpusHttp: OGG processing result: input=4096, output=4016, pages=1, efficiency=98.0%
I (23694) OpusHttp: OGG→Opus conversion: input=4096→4016 bytes, total_efficiency=76.1% (7327/9632)
I (23714) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6175/16384
I (23714) OpusHttp: Found Opus audio page: 4064 bytes (page #3, total_size=4108)
I (23724) OpusHttp: Extracted Opus data: 4064 bytes (page #1 in this batch)
I (23724) OpusHttp: OGG processing result: input=4096, output=4064, pages=1, efficiency=99.2%
I (23734) OpusHttp: OGG→Opus conversion: input=4096→4064 bytes, total_efficiency=83.0% (11391/13728)
I (23744) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6163/16384
I (23754) OpusHttp: Found Opus audio page: 4059 bytes (page #4, total_size=4103)
I (23764) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (23764) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (23774) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=86.7% (15450/17824)
I (23784) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6156/16384
I (23794) OpusHttp: Found Opus audio page: 4063 bytes (page #5, total_size=4107)
I (23794) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (23804) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (23814) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=89.0% (19513/21920)
I (23824) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6145/16384
I (23824) OpusHttp: Found Opus audio page: 4063 bytes (page #6, total_size=4107)
I (23834) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (23844) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (23854) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=90.6% (23576/26016)
I (23864) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6134/16384
I (23864) OpusHttp: Found Opus audio page: 4062 bytes (page #7, total_size=4106)
I (23874) OpusHttp: Extracted Opus data: 4062 bytes (page #1 in this batch)
I (23884) OpusHttp: OGG processing result: input=4096, output=4062, pages=1, efficiency=99.2%
I (23884) OpusHttp: OGG→Opus conversion: input=4096→4062 bytes, total_efficiency=91.8% (27638/30112)
I (23894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6124/16384
I (23904) OpusHttp: Found Opus audio page: 4057 bytes (page #8, total_size=4101)
I (23914) OpusHttp: Extracted Opus data: 4057 bytes (page #1 in this batch)
I (23914) OpusHttp: OGG processing result: input=4096, output=4057, pages=1, efficiency=99.0%
I (23924) OpusHttp: OGG→Opus conversion: input=4096→4057 bytes, total_efficiency=92.7% (31695/34208)
I (23934) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6119/16384
I (23944) OpusHttp: Found Opus audio page: 4007 bytes (page #9, total_size=4051)
I (23944) OpusHttp: Extracted Opus data: 4007 bytes (page #1 in this batch)
I (23954) OpusHttp: OGG processing result: input=4096, output=4007, pages=1, efficiency=97.8%
I (23964) OpusHttp: OGG→Opus conversion: input=4096→4007 bytes, total_efficiency=93.2% (35702/38304)
I (23974) OpusHttp: Start playing (buffered: 34 KB)
I (23974) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6164/16384
I (23984) OpusHttp: Found Opus audio page: 4058 bytes (page #10, total_size=4102)
I (23994) OpusHttp: Extracted Opus data: 4058 bytes (page #1 in this batch)
I (23994) OpusHttp: OGG processing result: input=4096, output=4058, pages=1, efficiency=99.1%
I (24004) OpusHttp: OGG→Opus conversion: input=4096→4058 bytes, total_efficiency=93.8% (39760/42400)
I (24014) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6158/16384
I (24024) OpusHttp: Found Opus audio page: 3818 bytes (page #11, total_size=3862)
I (24034) OpusHttp: Extracted Opus data: 3818 bytes (page #1 in this batch)
I (24034) OpusHttp: OGG processing result: input=4096, output=3818, pages=1, efficiency=93.2%
I (24044) OpusHttp: OGG→Opus conversion: input=4096→3818 bytes, total_efficiency=93.7% (43578/46496)
I (24054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6392/16384
I (24064) OpusHttp: Found Opus audio page: 3974 bytes (page #12, total_size=4018)
I (24064) OpusHttp: Extracted Opus data: 3974 bytes (page #1 in this batch)
I (24074) OpusHttp: OGG processing result: input=4096, output=3974, pages=1, efficiency=97.0%
I (24084) OpusHttp: OGG→Opus conversion: input=4096→3974 bytes, total_efficiency=94.0% (47552/50592)
I (24094) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6470/16384
I (24094) OpusHttp: Found Opus audio page: 4052 bytes (page #13, total_size=4096)
I (24104) OpusHttp: Extracted Opus data: 4052 bytes (page #1 in this batch)
I (24114) OpusHttp: OGG processing result: input=4096, output=4052, pages=1, efficiency=98.9%
I (24124) OpusHttp: OGG→Opus conversion: input=4096→4052 bytes, total_efficiency=94.4% (51604/54688)
I (24134) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6470/16384
I (24134) OpusHttp: Found Opus audio page: 3983 bytes (page #14, total_size=4027)
I (24144) OpusHttp: Extracted Opus data: 3983 bytes (page #1 in this batch)
I (24154) OpusHttp: OGG processing result: input=4096, output=3983, pages=1, efficiency=97.2%
I (24154) OpusHttp: OGG→Opus conversion: input=4096→3983 bytes, total_efficiency=94.6% (55587/58784)
I (24164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6539/16384
I (24174) OpusHttp: Found Opus audio page: 3977 bytes (page #15, total_size=4021)
I (24184) OpusHttp: Extracted Opus data: 3977 bytes (page #1 in this batch)
I (24184) OpusHttp: OGG processing result: input=4096, output=3977, pages=1, efficiency=97.1%
I (24194) OpusHttp: OGG→Opus conversion: input=4096→3977 bytes, total_efficiency=94.7% (59564/62880)
I (24204) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6614/16384
I (24214) OpusHttp: Found Opus audio page: 3959 bytes (page #16, total_size=4003)
I (24214) OpusHttp: Extracted Opus data: 3959 bytes (page #1 in this batch)
I (24224) OpusHttp: OGG processing result: input=4096, output=3959, pages=1, efficiency=96.7%
I (24234) OpusHttp: OGG→Opus conversion: input=4096→3959 bytes, total_efficiency=94.8% (63523/66976)
I (24244) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6707/16384
I (24254) OpusHttp: Found Opus audio page: 4009 bytes (page #17, total_size=4053)
I (24254) OpusHttp: Extracted Opus data: 4009 bytes (page #1 in this batch)
I (24264) OpusHttp: OGG processing result: input=4096, output=4009, pages=1, efficiency=97.9%
I (24274) OpusHttp: OGG→Opus conversion: input=4096→4009 bytes, total_efficiency=95.0% (67532/71072)
I (24284) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6750/16384
I (24284) OpusHttp: Found Opus audio page: 4063 bytes (page #18, total_size=4107)
I (24294) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (24304) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (24314) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=95.2% (71595/75168)
I (24324) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6739/16384
I (24324) OpusHttp: Found Opus audio page: 4036 bytes (page #19, total_size=4080)
I (24334) OpusHttp: Extracted Opus data: 4036 bytes (page #1 in this batch)
I (24344) OpusHttp: OGG processing result: input=4096, output=4036, pages=1, efficiency=98.5%
I (24344) OpusHttp: OGG→Opus conversion: input=4096→4036 bytes, total_efficiency=95.4% (75631/79264)
I (24354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6755/16384
I (24364) OpusHttp: Found Opus audio page: 3881 bytes (page #20, total_size=3925)
I (24374) OpusHttp: Extracted Opus data: 3881 bytes (page #1 in this batch)
I (24374) OpusHttp: OGG processing result: input=4096, output=3881, pages=1, efficiency=94.8%
I (24384) OpusHttp: OGG→Opus conversion: input=4096→3881 bytes, total_efficiency=95.4% (79512/83360)
I (24394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6926/16384
I (24404) OpusHttp: Found Opus audio page: 4045 bytes (page #21, total_size=4089)
I (24404) OpusHttp: Extracted Opus data: 4045 bytes (page #1 in this batch)
I (24414) OpusHttp: OGG processing result: input=4096, output=4045, pages=1, efficiency=98.8%
I (24424) OpusHttp: OGG→Opus conversion: input=4096→4045 bytes, total_efficiency=95.5% (83557/87456)
I (24434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6933/16384
I (24444) OpusHttp: Found Opus audio page: 3911 bytes (page #22, total_size=3955)
I (24444) OpusHttp: Extracted Opus data: 3911 bytes (page #1 in this batch)
I (24454) OpusHttp: OGG processing result: input=4096, output=3911, pages=1, efficiency=95.5%
I (24464) OpusHttp: OGG→Opus conversion: input=4096→3911 bytes, total_efficiency=95.5% (87468/91552)
I (24474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7074/16384
I (24474) OpusHttp: Found Opus audio page: 3963 bytes (page #23, total_size=4007)
I (24484) OpusHttp: Extracted Opus data: 3963 bytes (page #1 in this batch)
I (24494) OpusHttp: OGG processing result: input=4096, output=3963, pages=1, efficiency=96.8%
I (24504) OpusHttp: OGG→Opus conversion: input=4096→3963 bytes, total_efficiency=95.6% (91431/95648)
I (24514) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7163/16384
I (24514) OpusHttp: Found Opus audio page: 3937 bytes (page #24, total_size=3981)
I (24524) OpusHttp: Extracted Opus data: 3937 bytes (page #1 in this batch)
I (24534) OpusHttp: OGG processing result: input=4096, output=3937, pages=1, efficiency=96.1%
I (24534) OpusHttp: OGG→Opus conversion: input=4096→3937 bytes, total_efficiency=95.6% (95368/99744)
I (24544) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7278/16384
I (24554) OpusHttp: Found Opus audio page: 4037 bytes (page #25, total_size=4081)
I (24564) OpusHttp: Extracted Opus data: 4037 bytes (page #1 in this batch)
I (24564) OpusHttp: OGG processing result: input=4096, output=4037, pages=1, efficiency=98.6%
I (24574) OpusHttp: OGG→Opus conversion: input=4096→4037 bytes, total_efficiency=95.7% (99405/103840)
I (24584) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7293/16384
I (24594) OpusHttp: Found Opus audio page: 4012 bytes (page #26, total_size=4056)
I (24594) OpusHttp: Extracted Opus data: 4012 bytes (page #1 in this batch)
I (24604) OpusHttp: OGG processing result: input=4096, output=4012, pages=1, efficiency=97.9%
I (24614) OpusHttp: OGG→Opus conversion: input=4096→4012 bytes, total_efficiency=95.8% (103417/107936)
I (24624) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7333/16384
I (24624) OpusHttp: Found Opus audio page: 4020 bytes (page #27, total_size=4064)
I (24634) OpusHttp: Extracted Opus data: 4020 bytes (page #1 in this batch)
I (24644) OpusHttp: OGG processing result: input=4096, output=4020, pages=1, efficiency=98.1%
I (24654) OpusHttp: OGG→Opus conversion: input=4096→4020 bytes, total_efficiency=95.9% (107437/112032)
I (24664) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7365/16384
I (24664) OpusHttp: Found Opus audio page: 3832 bytes (page #28, total_size=3876)
I (24674) OpusHttp: Extracted Opus data: 3832 bytes (page #1 in this batch)
I (24684) OpusHttp: OGG processing result: input=4096, output=3832, pages=1, efficiency=93.6%
I (24694) OpusHttp: OGG→Opus conversion: input=4096→3832 bytes, total_efficiency=95.8% (111269/116128)
I (24704) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7585/16384
I (24704) OpusHttp: Found Opus audio page: 4059 bytes (page #29, total_size=4103)
I (24714) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (24724) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (24724) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=95.9% (115328/120224)
I (24734) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7578/16384
I (24744) OpusHttp: Found Opus audio page: 3937 bytes (page #30, total_size=3981)
I (24754) OpusHttp: Extracted Opus data: 3937 bytes (page #1 in this batch)
I (24754) OpusHttp: OGG processing result: input=4096, output=3937, pages=1, efficiency=96.1%
I (24764) OpusHttp: OGG→Opus conversion: input=4096→3937 bytes, total_efficiency=95.9% (119265/124320)
I (24774) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7693/16384
I (24784) OpusHttp: Found Opus audio page: 4054 bytes (page #31, total_size=4098)
I (24784) OpusHttp: Extracted Opus data: 4054 bytes (page #1 in this batch)
I (24794) OpusHttp: OGG processing result: input=4096, output=4054, pages=1, efficiency=99.0%
I (24804) OpusHttp: OGG→Opus conversion: input=4096→4054 bytes, total_efficiency=96.0% (123319/128416)
I (24814) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7691/16384
I (24824) OpusHttp: Found Opus audio page: 4004 bytes (page #32, total_size=4048)
I (24824) OpusHttp: Extracted Opus data: 4004 bytes (page #1 in this batch)
I (24834) OpusHttp: OGG processing result: input=4096, output=4004, pages=1, efficiency=97.8%
I (24844) OpusHttp: OGG→Opus conversion: input=4096→4004 bytes, total_efficiency=96.1% (127323/132512)
I (24854) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7739/16384
I (24854) OpusHttp: Found Opus audio page: 3820 bytes (page #33, total_size=3864)
I (24864) OpusHttp: Extracted Opus data: 3820 bytes (page #1 in this batch)
I (24874) OpusHttp: OGG processing result: input=4096, output=3820, pages=1, efficiency=93.3%
I (24884) OpusHttp: OGG→Opus conversion: input=4096→3820 bytes, total_efficiency=96.0% (131143/136608)
I (24894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7971/16384
I (24894) OpusHttp: Found Opus audio page: 4027 bytes (page #34, total_size=4071)
I (24904) OpusHttp: Extracted Opus data: 4027 bytes (page #1 in this batch)
I (24914) OpusHttp: OGG processing result: input=4096, output=4027, pages=1, efficiency=98.3%
I (24914) OpusHttp: OGG→Opus conversion: input=4096→4027 bytes, total_efficiency=96.1% (135170/140704)
I (24924) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7996/16384
I (24934) OpusHttp: Found Opus audio page: 3911 bytes (page #35, total_size=3955)
I (24944) OpusHttp: Extracted Opus data: 3911 bytes (page #1 in this batch)
I (24944) OpusHttp: OGG processing result: input=4096, output=3911, pages=1, efficiency=95.5%
I (24954) OpusHttp: OGG→Opus conversion: input=4096→3911 bytes, total_efficiency=96.1% (139081/144800)
I (24964) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8137/16384
I (24974) OpusHttp: Found Opus audio page: 4056 bytes (page #36, total_size=4100)
I (24984) OpusHttp: Extracted Opus data: 4056 bytes (page #1 in this batch)
I (24984) OpusHttp: Found Opus audio page: 3945 bytes (page #37, total_size=3989)
I (24994) OpusHttp: OGG processing result: input=4096, output=4056, pages=1, efficiency=99.0%
I (25004) OpusHttp: OGG→Opus conversion: input=4096→4056 bytes, total_efficiency=96.1% (143137/148896)
I (25014) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4144/16384
I (25014) OpusHttp: Found Opus audio page: 3912 bytes (page #38, total_size=3956)
I (25024) OpusHttp: Extracted Opus data: 3912 bytes (page #1 in this batch)
I (25034) OpusHttp: OGG processing result: input=4096, output=3912, pages=1, efficiency=95.5%
I (25044) OpusHttp: OGG→Opus conversion: input=4096→3912 bytes, total_efficiency=96.1% (147049/152992)
I (25054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4284/16384
I (25054) OpusHttp: Found Opus audio page: 3995 bytes (page #39, total_size=4039)
I (25064) OpusHttp: Extracted Opus data: 3995 bytes (page #1 in this batch)
I (25074) OpusHttp: OGG processing result: input=4096, output=3995, pages=1, efficiency=97.5%
I (25074) OpusHttp: OGG→Opus conversion: input=4096→3995 bytes, total_efficiency=96.2% (151044/157088)
I (25084) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4341/16384
I (25094) OpusHttp: Found Opus audio page: 4048 bytes (page #40, total_size=4092)
I (25104) OpusHttp: Extracted Opus data: 4048 bytes (page #1 in this batch)
I (25104) OpusHttp: OGG processing result: input=4096, output=4048, pages=1, efficiency=98.8%
I (25114) OpusHttp: OGG→Opus conversion: input=4096→4048 bytes, total_efficiency=96.2% (155092/161184)
I (25124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4345/16384
I (25134) OpusHttp: Found Opus audio page: 3979 bytes (page #41, total_size=4023)
I (25134) OpusHttp: Extracted Opus data: 3979 bytes (page #1 in this batch)
I (25144) OpusHttp: OGG processing result: input=4096, output=3979, pages=1, efficiency=97.1%
I (25154) OpusHttp: OGG→Opus conversion: input=4096→3979 bytes, total_efficiency=96.2% (159071/165280)
I (25164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4418/16384
I (25174) OpusHttp: Found Opus audio page: 4000 bytes (page #42, total_size=4044)
I (25174) OpusHttp: Extracted Opus data: 4000 bytes (page #1 in this batch)
I (25184) OpusHttp: OGG processing result: input=4096, output=4000, pages=1, efficiency=97.7%
I (25194) OpusHttp: OGG→Opus conversion: input=4096→4000 bytes, total_efficiency=96.3% (163071/169376)
I (25204) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4470/16384
I (25204) OpusHttp: Found Opus audio page: 3816 bytes (page #43, total_size=3860)
I (25214) OpusHttp: Extracted Opus data: 3816 bytes (page #1 in this batch)
I (25224) OpusHttp: OGG processing result: input=4096, output=3816, pages=1, efficiency=93.2%
I (25234) OpusHttp: OGG→Opus conversion: input=4096→3816 bytes, total_efficiency=96.2% (166887/173472)
I (25244) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4706/16384
I (25244) OpusHttp: Found Opus audio page: 4013 bytes (page #44, total_size=4057)
I (25254) OpusHttp: Extracted Opus data: 4013 bytes (page #1 in this batch)
I (25264) OpusHttp: OGG processing result: input=4096, output=4013, pages=1, efficiency=98.0%
I (25264) OpusHttp: OGG→Opus conversion: input=4096→4013 bytes, total_efficiency=96.2% (170900/177568)
I (25284) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4745/16384
I (25284) OpusHttp: Found Opus audio page: 4063 bytes (page #45, total_size=4107)
I (25294) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (25294) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (25304) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=96.3% (174963/181664)
I (25314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4734/16384
I (25324) OpusHttp: Found Opus audio page: 4058 bytes (page #46, total_size=4102)
I (25334) OpusHttp: Extracted Opus data: 4058 bytes (page #1 in this batch)
I (25334) OpusHttp: OGG processing result: input=4096, output=4058, pages=1, efficiency=99.1%
I (25344) OpusHttp: OGG→Opus conversion: input=4096→4058 bytes, total_efficiency=96.4% (179021/185760)
I (25354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4728/16384
I (25364) OpusHttp: Found Opus audio page: 4030 bytes (page #47, total_size=4074)
I (25364) OpusHttp: Extracted Opus data: 4030 bytes (page #1 in this batch)
I (25374) OpusHttp: OGG processing result: input=4096, output=4030, pages=1, efficiency=98.4%
I (25384) OpusHttp: OGG→Opus conversion: input=4096→4030 bytes, total_efficiency=96.4% (183051/189856)
I (25394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4750/16384
I (25394) OpusHttp: Found Opus audio page: 4065 bytes (page #48, total_size=4109)
I (25404) OpusHttp: Extracted Opus data: 4065 bytes (page #1 in this batch)
I (25414) OpusHttp: OGG processing result: input=4096, output=4065, pages=1, efficiency=99.2%
I (25424) OpusHttp: OGG→Opus conversion: input=4096→4065 bytes, total_efficiency=96.5% (187116/193952)
I (25434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4737/16384
I (25434) OpusHttp: Found Opus audio page: 3985 bytes (page #49, total_size=4029)
I (25444) OpusHttp: Extracted Opus data: 3985 bytes (page #1 in this batch)
I (25454) OpusHttp: OGG processing result: input=4096, output=3985, pages=1, efficiency=97.3%
I (25454) OpusHttp: OGG→Opus conversion: input=4096→3985 bytes, total_efficiency=96.5% (191101/198048)
I (25474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4804/16384
I (25474) OpusHttp: Found Opus audio page: 3898 bytes (page #50, total_size=3942)
I (25484) OpusHttp: Extracted Opus data: 3898 bytes (page #1 in this batch)
I (25484) OpusHttp: OGG processing result: input=4096, output=3898, pages=1, efficiency=95.2%
I (25494) OpusHttp: OGG→Opus conversion: input=4096→3898 bytes, total_efficiency=96.5% (194999/202144)
I (25504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4958/16384
I (25514) OpusHttp: Found Opus audio page: 3908 bytes (page #51, total_size=3952)
I (25524) OpusHttp: Extracted Opus data: 3908 bytes (page #1 in this batch)
I (25524) OpusHttp: OGG processing result: input=4096, output=3908, pages=1, efficiency=95.4%
I (25534) OpusHttp: OGG→Opus conversion: input=4096→3908 bytes, total_efficiency=96.4% (198907/206240)
I (25544) OpusHttp: OGG buffer: input=1120, copy=1120, buffered=2126/16384
I (25564) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6222/16384
I (25564) OpusHttp: Found Opus audio page: 3973 bytes (page #52, total_size=4017)
I (25574) OpusHttp: Extracted Opus data: 3973 bytes (page #1 in this batch)
I (25574) OpusHttp: OGG processing result: input=4096, output=3973, pages=1, efficiency=97.0%
I (25584) OpusHttp: OGG→Opus conversion: input=4096→3973 bytes, total_efficiency=95.9% (202880/211456)
I (25594) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6301/16384
I (25604) OpusHttp: Found Opus audio page: 3690 bytes (page #53, total_size=3734)
I (25604) OpusHttp: Extracted Opus data: 3690 bytes (page #1 in this batch)
I (25614) OpusHttp: OGG processing result: input=4096, output=3690, pages=1, efficiency=90.1%
I (25624) OpusHttp: OGG→Opus conversion: input=4096→3690 bytes, total_efficiency=95.8% (206570/215552)
I (25634) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6663/16384
I (25634) OpusHttp: Found Opus audio page: 4012 bytes (page #54, total_size=4056)
I (25644) OpusHttp: Extracted Opus data: 4012 bytes (page #1 in this batch)
I (25654) OpusHttp: OGG processing result: input=4096, output=4012, pages=1, efficiency=97.9%
I (25664) OpusHttp: OGG→Opus conversion: input=4096→4012 bytes, total_efficiency=95.9% (210582/219648)
I (25674) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6703/16384
I (25674) OpusHttp: Found Opus audio page: 3970 bytes (page #55, total_size=4014)
I (25684) OpusHttp: Extracted Opus data: 3970 bytes (page #1 in this batch)
I (25694) OpusHttp: OGG processing result: input=4096, output=3970, pages=1, efficiency=96.9%
I (25694) OpusHttp: OGG→Opus conversion: input=4096→3970 bytes, total_efficiency=95.9% (214552/223744)
I (25714) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6785/16384
I (25714) OpusHttp: Found Opus audio page: 3970 bytes (page #56, total_size=4014)
I (25724) OpusHttp: Extracted Opus data: 3970 bytes (page #1 in this batch)
I (25724) OpusHttp: OGG processing result: input=4096, output=3970, pages=1, efficiency=96.9%
I (25734) OpusHttp: OGG→Opus conversion: input=4096→3970 bytes, total_efficiency=95.9% (218522/227840)
I (25744) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6867/16384
I (25754) OpusHttp: Found Opus audio page: 3829 bytes (page #57, total_size=3873)
I (25764) OpusHttp: Extracted Opus data: 3829 bytes (page #1 in this batch)
I (25764) OpusHttp: OGG processing result: input=4096, output=3829, pages=1, efficiency=93.5%
I (25774) OpusHttp: OGG→Opus conversion: input=4096→3829 bytes, total_efficiency=95.9% (222351/231936)
I (25784) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7090/16384
I (25794) OpusHttp: Found Opus audio page: 3884 bytes (page #58, total_size=3928)
I (25794) OpusHttp: Extracted Opus data: 3884 bytes (page #1 in this batch)
I (25804) OpusHttp: OGG processing result: input=4096, output=3884, pages=1, efficiency=94.8%
I (25814) OpusHttp: OGG→Opus conversion: input=4096→3884 bytes, total_efficiency=95.8% (226235/236032)
I (25824) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7258/16384
I (25824) OpusHttp: Found Opus audio page: 3899 bytes (page #59, total_size=3943)
I (25834) OpusHttp: Extracted Opus data: 3899 bytes (page #1 in this batch)
I (25844) OpusHttp: OGG processing result: input=4096, output=3899, pages=1, efficiency=95.2%
I (25854) OpusHttp: OGG→Opus conversion: input=4096→3899 bytes, total_efficiency=95.8% (230134/240128)
I (25864) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7411/16384
I (25864) OpusHttp: Found Opus audio page: 4012 bytes (page #60, total_size=4056)
I (25874) OpusHttp: Extracted Opus data: 4012 bytes (page #1 in this batch)
I (25884) OpusHttp: OGG processing result: input=4096, output=4012, pages=1, efficiency=97.9%
I (25894) OpusHttp: OGG→Opus conversion: input=4096→4012 bytes, total_efficiency=95.9% (234146/244224)
I (25904) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7451/16384
I (25904) OpusHttp: Found Opus audio page: 3994 bytes (page #61, total_size=4038)
I (25914) OpusHttp: Extracted Opus data: 3994 bytes (page #1 in this batch)
I (25924) OpusHttp: OGG processing result: input=4096, output=3994, pages=1, efficiency=97.5%
I (25924) OpusHttp: OGG→Opus conversion: input=4096→3994 bytes, total_efficiency=95.9% (238140/248320)
I (25944) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7509/16384
I (25944) OpusHttp: Found Opus audio page: 3944 bytes (page #62, total_size=3988)
I (25954) OpusHttp: Extracted Opus data: 3944 bytes (page #1 in this batch)
I (25954) OpusHttp: OGG processing result: input=4096, output=3944, pages=1, efficiency=96.3%
I (25964) OpusHttp: OGG→Opus conversion: input=4096→3944 bytes, total_efficiency=95.9% (242084/252416)
I (25974) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7617/16384
I (25984) OpusHttp: Found Opus audio page: 3849 bytes (page #63, total_size=3893)
I (25984) OpusHttp: Extracted Opus data: 3849 bytes (page #1 in this batch)
I (25994) OpusHttp: OGG processing result: input=4096, output=3849, pages=1, efficiency=94.0%
I (26004) OpusHttp: OGG→Opus conversion: input=4096→3849 bytes, total_efficiency=95.9% (245933/256512)
I (26014) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7820/16384
I (26024) OpusHttp: Found Opus audio page: 4042 bytes (page #64, total_size=4086)
I (26024) OpusHttp: Extracted Opus data: 4042 bytes (page #1 in this batch)
I (26034) OpusHttp: OGG processing result: input=4096, output=4042, pages=1, efficiency=98.7%
I (26044) OpusHttp: OGG→Opus conversion: input=4096→4042 bytes, total_efficiency=95.9% (249975/260608)
I (26054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7830/16384
I (26054) OpusHttp: Found Opus audio page: 3964 bytes (page #65, total_size=4008)
I (26064) OpusHttp: Extracted Opus data: 3964 bytes (page #1 in this batch)
I (26074) OpusHttp: OGG processing result: input=4096, output=3964, pages=1, efficiency=96.8%
I (26084) OpusHttp: OGG→Opus conversion: input=4096→3964 bytes, total_efficiency=95.9% (253939/264704)
I (26094) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7918/16384
I (26094) OpusHttp: Found Opus audio page: 3994 bytes (page #66, total_size=4038)
I (26104) OpusHttp: Extracted Opus data: 3994 bytes (page #1 in this batch)
I (26114) OpusHttp: OGG processing result: input=4096, output=3994, pages=1, efficiency=97.5%
I (26114) OpusHttp: OGG→Opus conversion: input=4096→3994 bytes, total_efficiency=96.0% (257933/268800)
I (26124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7976/16384
I (26134) OpusHttp: Found Opus audio page: 3999 bytes (page #67, total_size=4043)
I (26144) OpusHttp: Extracted Opus data: 3999 bytes (page #1 in this batch)
I (26144) OpusHttp: OGG processing result: input=4096, output=3999, pages=1, efficiency=97.6%
I (26154) OpusHttp: OGG→Opus conversion: input=4096→3999 bytes, total_efficiency=96.0% (261932/272896)
I (26164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8029/16384
I (26174) OpusHttp: Found Opus audio page: 3983 bytes (page #68, total_size=4027)
I (26184) OpusHttp: Extracted Opus data: 3983 bytes (page #1 in this batch)
I (26184) OpusHttp: Found Opus audio page: 3949 bytes (page #69, total_size=3993)
I (26194) OpusHttp: OGG processing result: input=4096, output=3983, pages=1, efficiency=97.2%
I (26204) OpusHttp: OGG→Opus conversion: input=4096→3983 bytes, total_efficiency=96.0% (265915/276992)
I (26214) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4105/16384
I (26214) OpusHttp: Found Opus audio page: 4029 bytes (page #70, total_size=4073)
I (26224) OpusHttp: Extracted Opus data: 4029 bytes (page #1 in this batch)
I (26234) OpusHttp: OGG processing result: input=4096, output=4029, pages=1, efficiency=98.4%
I (26244) OpusHttp: OGG→Opus conversion: input=4096→4029 bytes, total_efficiency=96.0% (269944/281088)
I (26254) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4128/16384
I (26254) OpusHttp: Found Opus audio page: 3923 bytes (page #71, total_size=3967)
I (26264) OpusHttp: Extracted Opus data: 3923 bytes (page #1 in this batch)
I (26274) OpusHttp: OGG processing result: input=4096, output=3923, pages=1, efficiency=95.8%
I (26274) OpusHttp: OGG→Opus conversion: input=4096→3923 bytes, total_efficiency=96.0% (273867/285184)
I (26294) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4257/16384
I (26294) OpusHttp: Found Opus audio page: 3944 bytes (page #72, total_size=3988)
I (26304) OpusHttp: Extracted Opus data: 3944 bytes (page #1 in this batch)
I (26304) OpusHttp: OGG processing result: input=4096, output=3944, pages=1, efficiency=96.3%
I (26314) OpusHttp: OGG→Opus conversion: input=4096→3944 bytes, total_efficiency=96.0% (277811/289280)
I (26324) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4365/16384
I (26334) OpusHttp: Found Opus audio page: 3981 bytes (page #73, total_size=4025)
I (26334) OpusHttp: Extracted Opus data: 3981 bytes (page #1 in this batch)
I (26344) OpusHttp: OGG processing result: input=4096, output=3981, pages=1, efficiency=97.2%
I (26354) OpusHttp: OGG→Opus conversion: input=4096→3981 bytes, total_efficiency=96.1% (281792/293376)
I (26364) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4436/16384
I (26374) OpusHttp: Found Opus audio page: 3831 bytes (page #74, total_size=3875)
I (26374) OpusHttp: Extracted Opus data: 3831 bytes (page #1 in this batch)
I (26384) OpusHttp: OGG processing result: input=4096, output=3831, pages=1, efficiency=93.5%
I (26394) OpusHttp: OGG→Opus conversion: input=4096→3831 bytes, total_efficiency=96.0% (285623/297472)
I (26404) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4657/16384
I (26404) OpusHttp: Found Opus audio page: 3892 bytes (page #75, total_size=3936)
I (26414) OpusHttp: Extracted Opus data: 3892 bytes (page #1 in this batch)
I (26424) OpusHttp: OGG processing result: input=4096, output=3892, pages=1, efficiency=95.0%
I (26434) OpusHttp: OGG→Opus conversion: input=4096→3892 bytes, total_efficiency=96.0% (289515/301568)
I (26444) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4817/16384
I (26444) OpusHttp: Found Opus audio page: 4007 bytes (page #76, total_size=4051)
I (26454) OpusHttp: Extracted Opus data: 4007 bytes (page #1 in this batch)
I (26464) OpusHttp: OGG processing result: input=4096, output=4007, pages=1, efficiency=97.8%
I (26464) OpusHttp: OGG→Opus conversion: input=4096→4007 bytes, total_efficiency=96.0% (293522/305664)
I (26484) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4862/16384
I (26484) OpusHttp: Found Opus audio page: 3961 bytes (page #77, total_size=4005)
I (26494) OpusHttp: Extracted Opus data: 3961 bytes (page #1 in this batch)
I (26494) OpusHttp: OGG processing result: input=4096, output=3961, pages=1, efficiency=96.7%
I (26504) OpusHttp: OGG→Opus conversion: input=4096→3961 bytes, total_efficiency=96.0% (297483/309760)
I (26514) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4953/16384
I (26524) OpusHttp: Found Opus audio page: 3891 bytes (page #78, total_size=3935)
I (26534) OpusHttp: Extracted Opus data: 3891 bytes (page #1 in this batch)
I (26534) OpusHttp: OGG processing result: input=4096, output=3891, pages=1, efficiency=95.0%
I (26544) OpusHttp: OGG→Opus conversion: input=4096→3891 bytes, total_efficiency=96.0% (301374/313856)
I (26554) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5114/16384
I (26564) OpusHttp: Found Opus audio page: 3851 bytes (page #79, total_size=3895)
I (26564) OpusHttp: Extracted Opus data: 3851 bytes (page #1 in this batch)
I (26574) OpusHttp: OGG processing result: input=4096, output=3851, pages=1, efficiency=94.0%
I (26584) OpusHttp: OGG→Opus conversion: input=4096→3851 bytes, total_efficiency=96.0% (305225/317952)
I (26594) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5315/16384
I (26594) OpusHttp: Found Opus audio page: 4022 bytes (page #80, total_size=4066)
I (26604) OpusHttp: Extracted Opus data: 4022 bytes (page #1 in this batch)
I (26614) OpusHttp: OGG processing result: input=4096, output=4022, pages=1, efficiency=98.2%
I (26624) OpusHttp: OGG→Opus conversion: input=4096→4022 bytes, total_efficiency=96.0% (309247/322048)
I (26634) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5345/16384
I (26634) OpusHttp: Found Opus audio page: 3994 bytes (page #81, total_size=4038)
I (26644) OpusHttp: Extracted Opus data: 3994 bytes (page #1 in this batch)
I (26654) OpusHttp: OGG processing result: input=4096, output=3994, pages=1, efficiency=97.5%
I (26654) OpusHttp: OGG→Opus conversion: input=4096→3994 bytes, total_efficiency=96.0% (313241/326144)
I (26674) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5403/16384
I (26674) OpusHttp: Found Opus audio page: 4014 bytes (page #82, total_size=4058)
I (26684) OpusHttp: Extracted Opus data: 4014 bytes (page #1 in this batch)
I (26684) OpusHttp: OGG processing result: input=4096, output=4014, pages=1, efficiency=98.0%
I (26694) OpusHttp: OGG→Opus conversion: input=4096→4014 bytes, total_efficiency=96.1% (317255/330240)
I (26704) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5441/16384
I (26714) OpusHttp: Found Opus audio page: 4043 bytes (page #83, total_size=4087)
I (26724) OpusHttp: Extracted Opus data: 4043 bytes (page #1 in this batch)
I (26724) OpusHttp: OGG processing result: input=4096, output=4043, pages=1, efficiency=98.7%
I (26734) OpusHttp: OGG→Opus conversion: input=4096→4043 bytes, total_efficiency=96.1% (321298/334336)
I (26744) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5450/16384
I (26754) OpusHttp: Found Opus audio page: 3951 bytes (page #84, total_size=3995)
I (26754) OpusHttp: Extracted Opus data: 3951 bytes (page #1 in this batch)
I (26764) OpusHttp: OGG processing result: input=4096, output=3951, pages=1, efficiency=96.5%
I (26774) OpusHttp: OGG→Opus conversion: input=4096→3951 bytes, total_efficiency=96.1% (325249/338432)
I (26784) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5551/16384
I (26784) OpusHttp: Found Opus audio page: 4000 bytes (page #85, total_size=4044)
I (26794) OpusHttp: Extracted Opus data: 4000 bytes (page #1 in this batch)
I (26804) OpusHttp: OGG processing result: input=4096, output=4000, pages=1, efficiency=97.7%
I (26814) OpusHttp: OGG→Opus conversion: input=4096→4000 bytes, total_efficiency=96.1% (329249/342528)
I (26824) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5603/16384
I (26824) OpusHttp: Found Opus audio page: 3938 bytes (page #86, total_size=3982)
I (26834) OpusHttp: Extracted Opus data: 3938 bytes (page #1 in this batch)
I (26844) OpusHttp: OGG processing result: input=4096, output=3938, pages=1, efficiency=96.1%
I (26854) OpusHttp: OGG→Opus conversion: input=4096→3938 bytes, total_efficiency=96.1% (333187/346624)
I (26864) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5717/16384
I (26864) OpusHttp: Found Opus audio page: 3972 bytes (page #87, total_size=4016)
I (26874) OpusHttp: Extracted Opus data: 3972 bytes (page #1 in this batch)
I (26874) OpusHttp: OGG processing result: input=4096, output=3972, pages=1, efficiency=97.0%
I (26884) OpusHttp: OGG→Opus conversion: input=4096→3972 bytes, total_efficiency=96.1% (337159/350720)
I (26894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5797/16384
I (26904) OpusHttp: Found Opus audio page: 3980 bytes (page #88, total_size=4024)
I (26914) OpusHttp: Extracted Opus data: 3980 bytes (page #1 in this batch)
I (26914) OpusHttp: OGG processing result: input=4096, output=3980, pages=1, efficiency=97.2%
I (26924) OpusHttp: OGG→Opus conversion: input=4096→3980 bytes, total_efficiency=96.1% (341139/354816)
I (26934) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5869/16384
I (26944) OpusHttp: Found Opus audio page: 4036 bytes (page #89, total_size=4080)
I (26944) OpusHttp: Extracted Opus data: 4036 bytes (page #1 in this batch)
I (26954) OpusHttp: OGG processing result: input=4096, output=4036, pages=1, efficiency=98.5%
I (26964) OpusHttp: OGG→Opus conversion: input=4096→4036 bytes, total_efficiency=96.2% (345175/358912)
I (26974) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5885/16384
I (26984) OpusHttp: Found Opus audio page: 3964 bytes (page #90, total_size=4008)
I (26984) OpusHttp: Extracted Opus data: 3964 bytes (page #1 in this batch)
I (26994) OpusHttp: OGG processing result: input=4096, output=3964, pages=1, efficiency=96.8%
I (27004) OpusHttp: OGG→Opus conversion: input=4096→3964 bytes, total_efficiency=96.2% (349139/363008)
I (27014) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5973/16384
I (27014) OpusHttp: Found Opus audio page: 4061 bytes (page #91, total_size=4105)
I (27024) OpusHttp: Extracted Opus data: 4061 bytes (page #1 in this batch)
I (27034) OpusHttp: OGG processing result: input=4096, output=4061, pages=1, efficiency=99.1%
I (27044) OpusHttp: OGG→Opus conversion: input=4096→4061 bytes, total_efficiency=96.2% (353200/367104)
I (27054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5964/16384
I (27054) OpusHttp: Found Opus audio page: 4037 bytes (page #92, total_size=4081)
I (27064) OpusHttp: Extracted Opus data: 4037 bytes (page #1 in this batch)
I (27074) OpusHttp: OGG processing result: input=4096, output=4037, pages=1, efficiency=98.6%
I (27074) OpusHttp: OGG→Opus conversion: input=4096→4037 bytes, total_efficiency=96.2% (357237/371200)
I (27084) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5979/16384
I (27094) OpusHttp: Found Opus audio page: 3998 bytes (page #93, total_size=4042)
I (27104) OpusHttp: Extracted Opus data: 3998 bytes (page #1 in this batch)
I (27104) OpusHttp: OGG processing result: input=4096, output=3998, pages=1, efficiency=97.6%
I (27114) OpusHttp: OGG→Opus conversion: input=4096→3998 bytes, total_efficiency=96.3% (361235/375296)
I (27124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6033/16384
I (27134) OpusHttp: Found Opus audio page: 4055 bytes (page #94, total_size=4099)
I (27134) OpusHttp: Extracted Opus data: 4055 bytes (page #1 in this batch)
I (27144) OpusHttp: OGG processing result: input=4096, output=4055, pages=1, efficiency=99.0%
I (27154) OpusHttp: OGG→Opus conversion: input=4096→4055 bytes, total_efficiency=96.3% (365290/379392)
I (27164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6030/16384
I (27174) OpusHttp: Found Opus audio page: 4011 bytes (page #95, total_size=4055)
I (27174) OpusHttp: Extracted Opus data: 4011 bytes (page #1 in this batch)
I (27184) OpusHttp: OGG processing result: input=4096, output=4011, pages=1, efficiency=97.9%
I (27194) OpusHttp: OGG→Opus conversion: input=4096→4011 bytes, total_efficiency=96.3% (369301/383488)
I (27204) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6071/16384
I (27204) OpusHttp: Found Opus audio page: 4063 bytes (page #96, total_size=4107)
I (27214) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (27224) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (27234) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=96.3% (373364/387584)
I (27244) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6060/16384
I (27244) OpusHttp: Found Opus audio page: 4065 bytes (page #97, total_size=4109)
I (27254) OpusHttp: Extracted Opus data: 4065 bytes (page #1 in this batch)
I (27264) OpusHttp: OGG processing result: input=4096, output=4065, pages=1, efficiency=99.2%
I (27264) OpusHttp: OGG→Opus conversion: input=4096→4065 bytes, total_efficiency=96.4% (377429/391680)
I (27284) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6047/16384
I (27284) OpusHttp: Found Opus audio page: 4059 bytes (page #98, total_size=4103)
I (27294) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (27294) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (27304) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=96.4% (381488/395776)
I (27314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6040/16384
I (27324) OpusHttp: Found Opus audio page: 3998 bytes (page #99, total_size=4042)
I (27334) OpusHttp: Extracted Opus data: 3998 bytes (page #1 in this batch)
I (27334) OpusHttp: OGG processing result: input=4096, output=3998, pages=1, efficiency=97.6%
I (27344) OpusHttp: OGG→Opus conversion: input=4096→3998 bytes, total_efficiency=96.4% (385486/399872)
I (27354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6094/16384
I (27364) OpusHttp: Found Opus audio page: 3938 bytes (page #100, total_size=3982)
I (27364) OpusHttp: Extracted Opus data: 3938 bytes (page #1 in this batch)
I (27374) OpusHttp: OGG processing result: input=4096, output=3938, pages=1, efficiency=96.1%
I (27384) OpusHttp: OGG→Opus conversion: input=4096→3938 bytes, total_efficiency=96.4% (389424/403968)
I (27394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6208/16384
I (27394) OpusHttp: Found Opus audio page: 3960 bytes (page #101, total_size=4004)
I (27404) OpusHttp: Extracted Opus data: 3960 bytes (page #1 in this batch)
I (27414) OpusHttp: OGG processing result: input=4096, output=3960, pages=1, efficiency=96.7%
I (27424) OpusHttp: OGG→Opus conversion: input=4096→3960 bytes, total_efficiency=96.4% (393384/408064)
I (27434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6300/16384
I (27434) OpusHttp: Found Opus audio page: 4051 bytes (page #102, total_size=4095)
I (27444) OpusHttp: Extracted Opus data: 4051 bytes (page #1 in this batch)
I (27454) OpusHttp: OGG processing result: input=4096, output=4051, pages=1, efficiency=98.9%
I (27454) OpusHttp: OGG→Opus conversion: input=4096→4051 bytes, total_efficiency=96.4% (397435/412160)
I (27474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6301/16384
I (27474) OpusHttp: Found Opus audio page: 3989 bytes (page #103, total_size=4033)
I (27484) OpusHttp: Extracted Opus data: 3989 bytes (page #1 in this batch)
I (27484) OpusHttp: OGG processing result: input=4096, output=3989, pages=1, efficiency=97.4%
I (27494) OpusHttp: OGG→Opus conversion: input=4096→3989 bytes, total_efficiency=96.4% (401424/416256)
I (27504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6364/16384
I (27514) OpusHttp: Found Opus audio page: 4008 bytes (page #104, total_size=4052)
I (27524) OpusHttp: Extracted Opus data: 4008 bytes (page #1 in this batch)
I (27524) OpusHttp: OGG processing result: input=4096, output=4008, pages=1, efficiency=97.9%
I (27534) OpusHttp: OGG→Opus conversion: input=4096→4008 bytes, total_efficiency=96.5% (405432/420352)
I (27544) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6408/16384
I (27554) OpusHttp: Found Opus audio page: 3980 bytes (page #105, total_size=4024)
I (27554) OpusHttp: Extracted Opus data: 3980 bytes (page #1 in this batch)
I (27564) OpusHttp: OGG processing result: input=4096, output=3980, pages=1, efficiency=97.2%
I (27574) OpusHttp: OGG→Opus conversion: input=4096→3980 bytes, total_efficiency=96.5% (409412/424448)
I (27584) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6480/16384
I (27594) OpusHttp: Found Opus audio page: 3872 bytes (page #106, total_size=3916)
I (27594) OpusHttp: Extracted Opus data: 3872 bytes (page #1 in this batch)
I (27604) OpusHttp: OGG processing result: input=4096, output=3872, pages=1, efficiency=94.5%
I (27614) OpusHttp: OGG→Opus conversion: input=4096→3872 bytes, total_efficiency=96.4% (413284/428544)
I (27624) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6660/16384
I (27624) OpusHttp: Found Opus audio page: 4048 bytes (page #107, total_size=4092)
I (27634) OpusHttp: Extracted Opus data: 4048 bytes (page #1 in this batch)
I (27644) OpusHttp: OGG processing result: input=4096, output=4048, pages=1, efficiency=98.8%
I (27654) OpusHttp: OGG→Opus conversion: input=4096→4048 bytes, total_efficiency=96.5% (417332/432640)
I (27664) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6664/16384
I (27664) OpusHttp: Found Opus audio page: 3967 bytes (page #108, total_size=4011)
I (27674) OpusHttp: Extracted Opus data: 3967 bytes (page #1 in this batch)
I (27684) OpusHttp: OGG processing result: input=4096, output=3967, pages=1, efficiency=96.9%
I (27684) OpusHttp: OGG→Opus conversion: input=4096→3967 bytes, total_efficiency=96.5% (421299/436736)
I (27704) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6749/16384
I (27704) OpusHttp: Found Opus audio page: 4059 bytes (page #109, total_size=4103)
I (27714) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (27714) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (27724) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=96.5% (425358/440832)
I (27734) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6742/16384
I (27744) OpusHttp: Found Opus audio page: 3994 bytes (page #110, total_size=4038)
I (27754) OpusHttp: Extracted Opus data: 3994 bytes (page #1 in this batch)
I (27754) OpusHttp: OGG processing result: input=4096, output=3994, pages=1, efficiency=97.5%
I (27764) OpusHttp: OGG→Opus conversion: input=4096→3994 bytes, total_efficiency=96.5% (429352/444928)
I (27774) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6800/16384
I (27784) OpusHttp: Found Opus audio page: 4016 bytes (page #111, total_size=4060)
I (27784) OpusHttp: Extracted Opus data: 4016 bytes (page #1 in this batch)
I (27794) OpusHttp: OGG processing result: input=4096, output=4016, pages=1, efficiency=98.0%
I (27804) OpusHttp: OGG→Opus conversion: input=4096→4016 bytes, total_efficiency=96.5% (433368/449024)
I (27814) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6836/16384
I (27814) OpusHttp: Found Opus audio page: 4004 bytes (page #112, total_size=4048)
I (27824) OpusHttp: Extracted Opus data: 4004 bytes (page #1 in this batch)
I (27834) OpusHttp: OGG processing result: input=4096, output=4004, pages=1, efficiency=97.8%
I (27844) OpusHttp: OGG→Opus conversion: input=4096→4004 bytes, total_efficiency=96.5% (437372/453120)
I (27854) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6884/16384
I (27854) OpusHttp: Found Opus audio page: 3956 bytes (page #113, total_size=4000)
I (27864) OpusHttp: Extracted Opus data: 3956 bytes (page #1 in this batch)
I (27874) OpusHttp: OGG processing result: input=4096, output=3956, pages=1, efficiency=96.6%
I (27874) OpusHttp: OGG→Opus conversion: input=4096→3956 bytes, total_efficiency=96.5% (441328/457216)
I (27894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6980/16384
I (27894) OpusHttp: Found Opus audio page: 4017 bytes (page #114, total_size=4061)
I (27904) OpusHttp: Extracted Opus data: 4017 bytes (page #1 in this batch)
I (27914) OpusHttp: OGG processing result: input=4096, output=4017, pages=1, efficiency=98.1%
I (27914) OpusHttp: OGG→Opus conversion: input=4096→4017 bytes, total_efficiency=96.5% (445345/461312)
I (27924) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7015/16384
I (27934) OpusHttp: Found Opus audio page: 3932 bytes (page #115, total_size=3976)
I (27944) OpusHttp: Extracted Opus data: 3932 bytes (page #1 in this batch)
I (27944) OpusHttp: OGG processing result: input=4096, output=3932, pages=1, efficiency=96.0%
I (27954) OpusHttp: OGG→Opus conversion: input=4096→3932 bytes, total_efficiency=96.5% (449277/465408)
I (27964) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7135/16384
I (27974) OpusHttp: Found Opus audio page: 4020 bytes (page #116, total_size=4064)
I (27974) OpusHttp: Extracted Opus data: 4020 bytes (page #1 in this batch)
I (27984) OpusHttp: OGG processing result: input=4096, output=4020, pages=1, efficiency=98.1%
I (27994) OpusHttp: OGG→Opus conversion: input=4096→4020 bytes, total_efficiency=96.5% (453297/469504)
I (28004) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7167/16384
I (28014) OpusHttp: Found Opus audio page: 3999 bytes (page #117, total_size=4043)
I (28014) OpusHttp: Extracted Opus data: 3999 bytes (page #1 in this batch)
I (28024) OpusHttp: OGG processing result: input=4096, output=3999, pages=1, efficiency=97.6%
I (28034) OpusHttp: OGG→Opus conversion: input=4096→3999 bytes, total_efficiency=96.6% (457296/473600)
I (28044) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7220/16384
I (28044) OpusHttp: Found Opus audio page: 3994 bytes (page #118, total_size=4038)
I (28054) OpusHttp: Extracted Opus data: 3994 bytes (page #1 in this batch)
I (28064) OpusHttp: OGG processing result: input=4096, output=3994, pages=1, efficiency=97.5%
I (28074) OpusHttp: OGG→Opus conversion: input=4096→3994 bytes, total_efficiency=96.6% (461290/477696)
I (28084) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7278/16384
I (28084) OpusHttp: Found Opus audio page: 4006 bytes (page #119, total_size=4050)
I (28094) OpusHttp: Extracted Opus data: 4006 bytes (page #1 in this batch)
I (28104) OpusHttp: OGG processing result: input=4096, output=4006, pages=1, efficiency=97.8%
I (28104) OpusHttp: OGG→Opus conversion: input=4096→4006 bytes, total_efficiency=96.6% (465296/481792)
I (28124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7324/16384
I (28124) OpusHttp: Found Opus audio page: 4059 bytes (page #120, total_size=4103)
I (28134) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (28134) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (28144) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=96.6% (469355/485888)
I (28154) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7317/16384
I (28164) OpusHttp: Found Opus audio page: 3990 bytes (page #121, total_size=4034)
I (28174) OpusHttp: Extracted Opus data: 3990 bytes (page #1 in this batch)
I (28174) OpusHttp: OGG processing result: input=4096, output=3990, pages=1, efficiency=97.4%
I (28184) OpusHttp: OGG→Opus conversion: input=4096→3990 bytes, total_efficiency=96.6% (473345/489984)
I (28194) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7379/16384
I (28204) OpusHttp: Found Opus audio page: 4062 bytes (page #122, total_size=4106)
I (28204) OpusHttp: Extracted Opus data: 4062 bytes (page #1 in this batch)
I (28214) OpusHttp: OGG processing result: input=4096, output=4062, pages=1, efficiency=99.2%
I (28224) OpusHttp: OGG→Opus conversion: input=4096→4062 bytes, total_efficiency=96.6% (477407/494080)
I (28234) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7369/16384
I (28234) OpusHttp: Found Opus audio page: 4043 bytes (page #123, total_size=4087)
I (28244) OpusHttp: Extracted Opus data: 4043 bytes (page #1 in this batch)
I (28254) OpusHttp: OGG processing result: input=4096, output=4043, pages=1, efficiency=98.7%
I (28264) OpusHttp: OGG→Opus conversion: input=4096→4043 bytes, total_efficiency=96.6% (481450/498176)
I (28274) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7378/16384
I (28274) OpusHttp: Found Opus audio page: 4022 bytes (page #124, total_size=4066)
I (28284) OpusHttp: Extracted Opus data: 4022 bytes (page #1 in this batch)
I (28294) OpusHttp: OGG processing result: input=4096, output=4022, pages=1, efficiency=98.2%
I (28304) OpusHttp: OGG→Opus conversion: input=4096→4022 bytes, total_efficiency=96.7% (485472/502272)
I (28314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7408/16384
I (28314) OpusHttp: Found Opus audio page: 3817 bytes (page #125, total_size=3861)
I (28324) OpusHttp: Extracted Opus data: 3817 bytes (page #1 in this batch)
I (28334) OpusHttp: OGG processing result: input=4096, output=3817, pages=1, efficiency=93.2%
I (28334) OpusHttp: OGG→Opus conversion: input=4096→3817 bytes, total_efficiency=96.6% (489289/506368)
I (28324) OpusHttp: Opus decode failed: 2 (len=964, buffer_available=0)
I (28354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7643/16384
I (28364) OpusHttp: Found Opus audio page: 3999 bytes (page #126, total_size=4043)
I (28364) OpusHttp: Extracted Opus data: 3999 bytes (page #1 in this batch)
I (28374) OpusHttp: OGG processing result: input=4096, output=3999, pages=1, efficiency=97.6%
I (28384) OpusHttp: OGG→Opus conversion: input=4096→3999 bytes, total_efficiency=96.6% (493288/510464)
I (28394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7696/16384
I (28394) OpusHttp: Found Opus audio page: 3938 bytes (page #127, total_size=3982)
I (28404) OpusHttp: Extracted Opus data: 3938 bytes (page #1 in this batch)
I (28414) OpusHttp: OGG processing result: input=4096, output=3938, pages=1, efficiency=96.1%
I (28424) OpusHttp: OGG→Opus conversion: input=4096→3938 bytes, total_efficiency=96.6% (497226/514560)
I (28434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7810/16384
I (28434) OpusHttp: Found Opus audio page: 4055 bytes (page #128, total_size=4099)
I (28444) OpusHttp: Extracted Opus data: 4055 bytes (page #1 in this batch)
I (28454) OpusHttp: OGG processing result: input=4096, output=4055, pages=1, efficiency=99.0%
I (28454) OpusHttp: OGG→Opus conversion: input=4096→4055 bytes, total_efficiency=96.6% (501281/518656)
I (28474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7807/16384
I (28474) OpusHttp: Found Opus audio page: 3800 bytes (page #129, total_size=3844)
I (28484) OpusHttp: Extracted Opus data: 3800 bytes (page #1 in this batch)
I (28484) OpusHttp: OGG processing result: input=4096, output=3800, pages=1, efficiency=92.8%
I (28494) OpusHttp: OGG→Opus conversion: input=4096→3800 bytes, total_efficiency=96.6% (505081/522752)
I (28504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8059/16384
I (28514) OpusHttp: Found Opus audio page: 4023 bytes (page #130, total_size=4067)
I (28524) OpusHttp: Extracted Opus data: 4023 bytes (page #1 in this batch)
I (28524) OpusHttp: OGG processing result: input=4096, output=4023, pages=1, efficiency=98.2%
I (28534) OpusHttp: OGG→Opus conversion: input=4096→4023 bytes, total_efficiency=96.6% (509104/526848)
I (28544) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8088/16384
I (28554) OpusHttp: Found Opus audio page: 4047 bytes (page #131, total_size=4091)
I (28554) OpusHttp: Extracted Opus data: 4047 bytes (page #1 in this batch)
I (28564) OpusHttp: OGG processing result: input=4096, output=4047, pages=1, efficiency=98.8%
I (28574) OpusHttp: OGG→Opus conversion: input=4096→4047 bytes, total_efficiency=96.6% (513151/530944)
I (28584) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8093/16384
I (28584) OpusHttp: Found Opus audio page: 4007 bytes (page #132, total_size=4051)
I (28594) OpusHttp: Extracted Opus data: 4007 bytes (page #1 in this batch)
I (28604) OpusHttp: OGG processing result: input=4096, output=4007, pages=1, efficiency=97.8%
I (28614) OpusHttp: OGG→Opus conversion: input=4096→4007 bytes, total_efficiency=96.7% (517158/535040)
I (28624) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=8138/16384
I (28624) OpusHttp: Found Opus audio page: 4016 bytes (page #133, total_size=4060)
I (28634) OpusHttp: Extracted Opus data: 4016 bytes (page #1 in this batch)
I (28644) OpusHttp: Found Opus audio page: 4031 bytes (page #134, total_size=4075)
I (28644) OpusHttp: OGG processing result: input=4096, output=4016, pages=1, efficiency=98.0%
I (28654) OpusHttp: OGG→Opus conversion: input=4096→4016 bytes, total_efficiency=96.7% (521174/539136)
I (28664) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4099/16384
I (28674) OpusHttp: Found Opus audio page: 3865 bytes (page #135, total_size=3909)
I (28684) OpusHttp: Extracted Opus data: 3865 bytes (page #1 in this batch)
I (28684) OpusHttp: OGG processing result: input=4096, output=3865, pages=1, efficiency=94.4%
I (28694) OpusHttp: OGG→Opus conversion: input=4096→3865 bytes, total_efficiency=96.7% (525039/543232)
I (28704) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4286/16384
I (28714) OpusHttp: Found Opus audio page: 3935 bytes (page #136, total_size=3979)
I (28714) OpusHttp: Extracted Opus data: 3935 bytes (page #1 in this batch)
I (28724) OpusHttp: OGG processing result: input=4096, output=3935, pages=1, efficiency=96.1%
I (28734) OpusHttp: OGG→Opus conversion: input=4096→3935 bytes, total_efficiency=96.6% (528974/547328)
I (28744) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4403/16384
I (28744) OpusHttp: Found Opus audio page: 4021 bytes (page #137, total_size=4065)
I (28754) OpusHttp: Extracted Opus data: 4021 bytes (page #1 in this batch)
I (28764) OpusHttp: OGG processing result: input=4096, output=4021, pages=1, efficiency=98.2%
I (28774) OpusHttp: OGG→Opus conversion: input=4096→4021 bytes, total_efficiency=96.7% (532995/551424)
I (28784) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4434/16384
I (28784) OpusHttp: Found Opus audio page: 3972 bytes (page #138, total_size=4016)
I (28794) OpusHttp: Extracted Opus data: 3972 bytes (page #1 in this batch)
I (28804) OpusHttp: OGG processing result: input=4096, output=3972, pages=1, efficiency=97.0%
I (28814) OpusHttp: OGG→Opus conversion: input=4096→3972 bytes, total_efficiency=96.7% (536967/555520)
I (28824) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4514/16384
I (28824) OpusHttp: Found Opus audio page: 3902 bytes (page #139, total_size=3946)
I (28834) OpusHttp: Extracted Opus data: 3902 bytes (page #1 in this batch)
I (28844) OpusHttp: OGG processing result: input=4096, output=3902, pages=1, efficiency=95.3%
I (28844) OpusHttp: OGG→Opus conversion: input=4096→3902 bytes, total_efficiency=96.7% (540869/559616)
I (28854) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4664/16384
I (28864) OpusHttp: Found Opus audio page: 3974 bytes (page #140, total_size=4018)
I (28874) OpusHttp: Extracted Opus data: 3974 bytes (page #1 in this batch)
I (28874) OpusHttp: OGG processing result: input=4096, output=3974, pages=1, efficiency=97.0%
I (28884) OpusHttp: OGG→Opus conversion: input=4096→3974 bytes, total_efficiency=96.7% (544843/563712)
I (28894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4742/16384
I (28904) OpusHttp: Found Opus audio page: 3889 bytes (page #141, total_size=3933)
I (28904) OpusHttp: Extracted Opus data: 3889 bytes (page #1 in this batch)
I (28914) OpusHttp: OGG processing result: input=4096, output=3889, pages=1, efficiency=94.9%
I (28924) OpusHttp: OGG→Opus conversion: input=4096→3889 bytes, total_efficiency=96.6% (548732/567808)
I (28934) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4905/16384
I (28944) OpusHttp: Found Opus audio page: 3950 bytes (page #142, total_size=3994)
I (28944) OpusHttp: Extracted Opus data: 3950 bytes (page #1 in this batch)
I (28954) OpusHttp: OGG processing result: input=4096, output=3950, pages=1, efficiency=96.4%
I (28964) OpusHttp: OGG→Opus conversion: input=4096→3950 bytes, total_efficiency=96.6% (552682/571904)
I (28974) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5007/16384
I (28974) OpusHttp: Found Opus audio page: 3965 bytes (page #143, total_size=4009)
I (28984) OpusHttp: Extracted Opus data: 3965 bytes (page #1 in this batch)
I (28994) OpusHttp: OGG processing result: input=4096, output=3965, pages=1, efficiency=96.8%
I (29004) OpusHttp: OGG→Opus conversion: input=4096→3965 bytes, total_efficiency=96.6% (556647/576000)
I (29014) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5094/16384
I (29014) OpusHttp: Found Opus audio page: 3998 bytes (page #144, total_size=4042)
I (29024) OpusHttp: Extracted Opus data: 3998 bytes (page #1 in this batch)
I (29034) OpusHttp: OGG processing result: input=4096, output=3998, pages=1, efficiency=97.6%
I (29034) OpusHttp: OGG→Opus conversion: input=4096→3998 bytes, total_efficiency=96.6% (560645/580096)
I (29054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5148/16384
I (29054) OpusHttp: Found Opus audio page: 3892 bytes (page #145, total_size=3936)
I (29064) OpusHttp: Extracted Opus data: 3892 bytes (page #1 in this batch)
I (29064) OpusHttp: OGG processing result: input=4096, output=3892, pages=1, efficiency=95.0%
I (29074) OpusHttp: OGG→Opus conversion: input=4096→3892 bytes, total_efficiency=96.6% (564537/584192)
I (29084) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5308/16384
I (29094) OpusHttp: Found Opus audio page: 3911 bytes (page #146, total_size=3955)
I (29104) OpusHttp: Extracted Opus data: 3911 bytes (page #1 in this batch)
I (29104) OpusHttp: OGG processing result: input=4096, output=3911, pages=1, efficiency=95.5%
I (29114) OpusHttp: OGG→Opus conversion: input=4096→3911 bytes, total_efficiency=96.6% (568448/588288)
I (29124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5449/16384
I (29134) OpusHttp: Found Opus audio page: 3904 bytes (page #147, total_size=3948)
I (29134) OpusHttp: Extracted Opus data: 3904 bytes (page #1 in this batch)
I (29144) OpusHttp: OGG processing result: input=4096, output=3904, pages=1, efficiency=95.3%
I (29154) OpusHttp: OGG→Opus conversion: input=4096→3904 bytes, total_efficiency=96.6% (572352/592384)
I (29164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5597/16384
I (29164) OpusHttp: Found Opus audio page: 3991 bytes (page #148, total_size=4035)
I (29174) OpusHttp: Extracted Opus data: 3991 bytes (page #1 in this batch)
I (29184) OpusHttp: OGG processing result: input=4096, output=3991, pages=1, efficiency=97.4%
I (29194) OpusHttp: OGG→Opus conversion: input=4096→3991 bytes, total_efficiency=96.6% (576343/596480)
I (29204) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5658/16384
I (29204) OpusHttp: Found Opus audio page: 3883 bytes (page #149, total_size=3927)
I (29214) OpusHttp: Extracted Opus data: 3883 bytes (page #1 in this batch)
I (29224) OpusHttp: OGG processing result: input=4096, output=3883, pages=1, efficiency=94.8%
I (29234) OpusHttp: OGG→Opus conversion: input=4096→3883 bytes, total_efficiency=96.6% (580226/600576)
I (29244) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5827/16384
I (29244) OpusHttp: Found Opus audio page: 3990 bytes (page #150, total_size=4034)
I (29254) OpusHttp: Extracted Opus data: 3990 bytes (page #1 in this batch)
I (29264) OpusHttp: OGG processing result: input=4096, output=3990, pages=1, efficiency=97.4%
I (29264) OpusHttp: OGG→Opus conversion: input=4096→3990 bytes, total_efficiency=96.6% (584216/604672)
I (29274) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5889/16384
I (29284) OpusHttp: Found Opus audio page: 4022 bytes (page #151, total_size=4066)
I (29294) OpusHttp: Extracted Opus data: 4022 bytes (page #1 in this batch)
I (29294) OpusHttp: OGG processing result: input=4096, output=4022, pages=1, efficiency=98.2%
I (29304) OpusHttp: OGG→Opus conversion: input=4096→4022 bytes, total_efficiency=96.6% (588238/608768)
I (29314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5919/16384
I (29324) OpusHttp: Found Opus audio page: 4021 bytes (page #152, total_size=4065)
I (29334) OpusHttp: Extracted Opus data: 4021 bytes (page #1 in this batch)
I (29334) OpusHttp: OGG processing result: input=4096, output=4021, pages=1, efficiency=98.2%
I (29344) OpusHttp: OGG→Opus conversion: input=4096→4021 bytes, total_efficiency=96.6% (592259/612864)
I (29354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5950/16384
I (29364) OpusHttp: Found Opus audio page: 3913 bytes (page #153, total_size=3957)
I (29364) OpusHttp: Extracted Opus data: 3913 bytes (page #1 in this batch)
I (29374) OpusHttp: OGG processing result: input=4096, output=3913, pages=1, efficiency=95.5%
I (29384) OpusHttp: OGG→Opus conversion: input=4096→3913 bytes, total_efficiency=96.6% (596172/616960)
I (29394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6089/16384
I (29394) OpusHttp: Found Opus audio page: 3864 bytes (page #154, total_size=3908)
I (29404) OpusHttp: Extracted Opus data: 3864 bytes (page #1 in this batch)
I (29414) OpusHttp: OGG processing result: input=4096, output=3864, pages=1, efficiency=94.3%
I (29424) OpusHttp: OGG→Opus conversion: input=4096→3864 bytes, total_efficiency=96.6% (600036/621056)
I (29434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6277/16384
I (29434) OpusHttp: Found Opus audio page: 3827 bytes (page #155, total_size=3871)
I (29444) OpusHttp: Extracted Opus data: 3827 bytes (page #1 in this batch)
I (29454) OpusHttp: OGG processing result: input=4096, output=3827, pages=1, efficiency=93.4%
I (29454) OpusHttp: OGG→Opus conversion: input=4096→3827 bytes, total_efficiency=96.6% (603863/625152)
I (29474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6502/16384
I (29474) OpusHttp: Found Opus audio page: 3974 bytes (page #156, total_size=4018)
I (29484) OpusHttp: Extracted Opus data: 3974 bytes (page #1 in this batch)
I (29484) OpusHttp: OGG processing result: input=4096, output=3974, pages=1, efficiency=97.0%
I (29494) OpusHttp: OGG→Opus conversion: input=4096→3974 bytes, total_efficiency=96.6% (607837/629248)
I (29504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6580/16384
I (29514) OpusHttp: Found Opus audio page: 4042 bytes (page #157, total_size=4086)
I (29524) OpusHttp: Extracted Opus data: 4042 bytes (page #1 in this batch)
I (29524) OpusHttp: OGG processing result: input=4096, output=4042, pages=1, efficiency=98.7%
I (29534) OpusHttp: OGG→Opus conversion: input=4096→4042 bytes, total_efficiency=96.6% (611879/633344)
I (29544) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6590/16384
I (29554) OpusHttp: Found Opus audio page: 3991 bytes (page #158, total_size=4035)
I (29554) OpusHttp: Extracted Opus data: 3991 bytes (page #1 in this batch)
I (29564) OpusHttp: OGG processing result: input=4096, output=3991, pages=1, efficiency=97.4%
I (29574) OpusHttp: OGG→Opus conversion: input=4096→3991 bytes, total_efficiency=96.6% (615870/637440)
I (29584) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6651/16384
I (29594) OpusHttp: Found Opus audio page: 3850 bytes (page #159, total_size=3894)
I (29594) OpusHttp: Extracted Opus data: 3850 bytes (page #1 in this batch)
I (29604) OpusHttp: OGG processing result: input=4096, output=3850, pages=1, efficiency=94.0%
I (29614) OpusHttp: OGG→Opus conversion: input=4096→3850 bytes, total_efficiency=96.6% (619720/641536)
I (29624) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6853/16384
I (29624) OpusHttp: Found Opus audio page: 3975 bytes (page #160, total_size=4019)
I (29634) OpusHttp: Extracted Opus data: 3975 bytes (page #1 in this batch)
I (29644) OpusHttp: OGG processing result: input=4096, output=3975, pages=1, efficiency=97.0%
I (29654) OpusHttp: OGG→Opus conversion: input=4096→3975 bytes, total_efficiency=96.6% (623695/645632)
I (29664) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6930/16384
I (29664) OpusHttp: Found Opus audio page: 3957 bytes (page #161, total_size=4001)
I (29674) OpusHttp: Extracted Opus data: 3957 bytes (page #1 in this batch)
I (29684) OpusHttp: OGG processing result: input=4096, output=3957, pages=1, efficiency=96.6%
I (29684) OpusHttp: OGG→Opus conversion: input=4096→3957 bytes, total_efficiency=96.6% (627652/649728)
I (29694) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7025/16384
I (29704) OpusHttp: Found Opus audio page: 3993 bytes (page #162, total_size=4037)
I (29714) OpusHttp: Extracted Opus data: 3993 bytes (page #1 in this batch)
I (29714) OpusHttp: OGG processing result: input=4096, output=3993, pages=1, efficiency=97.5%
I (29724) OpusHttp: OGG→Opus conversion: input=4096→3993 bytes, total_efficiency=96.6% (631645/653824)
I (29734) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7084/16384
I (29744) OpusHttp: Found Opus audio page: 3852 bytes (page #163, total_size=3896)
I (29754) OpusHttp: Extracted Opus data: 3852 bytes (page #1 in this batch)
I (29754) OpusHttp: OGG processing result: input=4096, output=3852, pages=1, efficiency=94.0%
I (29764) OpusHttp: OGG→Opus conversion: input=4096→3852 bytes, total_efficiency=96.6% (635497/657920)
I (29774) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7284/16384
I (29784) OpusHttp: Found Opus audio page: 3944 bytes (page #164, total_size=3988)
I (29784) OpusHttp: Extracted Opus data: 3944 bytes (page #1 in this batch)
I (29794) OpusHttp: OGG processing result: input=4096, output=3944, pages=1, efficiency=96.3%
I (29804) OpusHttp: OGG→Opus conversion: input=4096→3944 bytes, total_efficiency=96.6% (639441/662016)
I (29814) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7392/16384
I (29814) OpusHttp: Found Opus audio page: 3958 bytes (page #165, total_size=4002)
I (29824) OpusHttp: Extracted Opus data: 3958 bytes (page #1 in this batch)
I (29834) OpusHttp: OGG processing result: input=4096, output=3958, pages=1, efficiency=96.6%
I (29844) OpusHttp: OGG→Opus conversion: input=4096→3958 bytes, total_efficiency=96.6% (643399/666112)
I (29854) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7486/16384
I (29854) OpusHttp: Found Opus audio page: 3868 bytes (page #166, total_size=3912)
I (29864) OpusHttp: Extracted Opus data: 3868 bytes (page #1 in this batch)
I (29874) OpusHttp: OGG processing result: input=4096, output=3868, pages=1, efficiency=94.4%
I (29874) OpusHttp: OGG→Opus conversion: input=4096→3868 bytes, total_efficiency=96.6% (647267/670208)
I (29894) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7670/16384
I (29894) OpusHttp: Found Opus audio page: 3996 bytes (page #167, total_size=4040)
I (29904) OpusHttp: Extracted Opus data: 3996 bytes (page #1 in this batch)
I (29904) OpusHttp: OGG processing result: input=4096, output=3996, pages=1, efficiency=97.6%
I (29914) OpusHttp: OGG→Opus conversion: input=4096→3996 bytes, total_efficiency=96.6% (651263/674304)
I (29924) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7726/16384
I (29934) OpusHttp: Found Opus audio page: 3954 bytes (page #168, total_size=3998)
I (29944) OpusHttp: Extracted Opus data: 3954 bytes (page #1 in this batch)
I (29944) OpusHttp: OGG processing result: input=4096, output=3954, pages=1, efficiency=96.5%
I (29954) OpusHttp: OGG→Opus conversion: input=4096→3954 bytes, total_efficiency=96.6% (655217/678400)
I (29964) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7824/16384
I (29974) OpusHttp: Found Opus audio page: 3992 bytes (page #169, total_size=4036)
I (29974) OpusHttp: Extracted Opus data: 3992 bytes (page #1 in this batch)
I (29984) OpusHttp: OGG processing result: input=4096, output=3992, pages=1, efficiency=97.5%
I (29994) OpusHttp: OGG→Opus conversion: input=4096→3992 bytes, total_efficiency=96.6% (659209/682496)
I (30004) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7884/16384
I (30014) OpusHttp: Found Opus audio page: 3849 bytes (page #170, total_size=3893)
I (30014) OpusHttp: Extracted Opus data: 3849 bytes (page #1 in this batch)
I (30024) OpusHttp: Found Opus audio page: 3751 bytes (page #171, total_size=3795)
I (30034) OpusHttp: OGG processing result: input=4096, output=3849, pages=1, efficiency=94.0%
I (30034) OpusHttp: OGG→Opus conversion: input=4096→3849 bytes, total_efficiency=96.6% (663058/686592)
I (30054) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4292/16384
I (30054) OpusHttp: Found Opus audio page: 3861 bytes (page #172, total_size=3905)
I (30064) OpusHttp: Extracted Opus data: 3861 bytes (page #1 in this batch)
I (30064) OpusHttp: OGG processing result: input=4096, output=3861, pages=1, efficiency=94.3%
I (30074) OpusHttp: OGG→Opus conversion: input=4096→3861 bytes, total_efficiency=96.6% (666919/690688)
I (30084) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4483/16384
I (30094) OpusHttp: Found Opus audio page: 4025 bytes (page #173, total_size=4069)
I (30104) OpusHttp: Extracted Opus data: 4025 bytes (page #1 in this batch)
I (30104) OpusHttp: OGG processing result: input=4096, output=4025, pages=1, efficiency=98.3%
I (30114) OpusHttp: OGG→Opus conversion: input=4096→4025 bytes, total_efficiency=96.6% (670944/694784)
I (30124) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4510/16384
I (30134) OpusHttp: Found Opus audio page: 4032 bytes (page #174, total_size=4076)
I (30134) OpusHttp: Extracted Opus data: 4032 bytes (page #1 in this batch)
I (30144) OpusHttp: OGG processing result: input=4096, output=4032, pages=1, efficiency=98.4%
I (30154) OpusHttp: OGG→Opus conversion: input=4096→4032 bytes, total_efficiency=96.6% (674976/698880)
I (30164) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4530/16384
I (30174) OpusHttp: Found Opus audio page: 4007 bytes (page #175, total_size=4051)
I (30174) OpusHttp: Extracted Opus data: 4007 bytes (page #1 in this batch)
I (30184) OpusHttp: OGG processing result: input=4096, output=4007, pages=1, efficiency=97.8%
I (30194) OpusHttp: OGG→Opus conversion: input=4096→4007 bytes, total_efficiency=96.6% (678983/702976)
I (30204) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4575/16384
I (30204) OpusHttp: Found Opus audio page: 3929 bytes (page #176, total_size=3973)
I (30214) OpusHttp: Extracted Opus data: 3929 bytes (page #1 in this batch)
I (30224) OpusHttp: OGG processing result: input=4096, output=3929, pages=1, efficiency=95.9%
I (30234) OpusHttp: OGG→Opus conversion: input=4096→3929 bytes, total_efficiency=96.6% (682912/707072)
I (30244) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4698/16384
I (30244) OpusHttp: Found Opus audio page: 3911 bytes (page #177, total_size=3955)
I (30254) OpusHttp: Extracted Opus data: 3911 bytes (page #1 in this batch)
I (30264) OpusHttp: OGG processing result: input=4096, output=3911, pages=1, efficiency=95.5%
I (30264) OpusHttp: OGG→Opus conversion: input=4096→3911 bytes, total_efficiency=96.6% (686823/711168)
I (30274) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4839/16384
I (30284) OpusHttp: Found Opus audio page: 4025 bytes (page #178, total_size=4069)
I (30294) OpusHttp: Extracted Opus data: 4025 bytes (page #1 in this batch)
I (30294) OpusHttp: OGG processing result: input=4096, output=4025, pages=1, efficiency=98.3%
I (30304) OpusHttp: OGG→Opus conversion: input=4096→4025 bytes, total_efficiency=96.6% (690848/715264)
I (30314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4866/16384
I (30324) OpusHttp: Found Opus audio page: 3830 bytes (page #179, total_size=3874)
I (30334) OpusHttp: Extracted Opus data: 3830 bytes (page #1 in this batch)
I (30334) OpusHttp: OGG processing result: input=4096, output=3830, pages=1, efficiency=93.5%
I (30344) OpusHttp: OGG→Opus conversion: input=4096→3830 bytes, total_efficiency=96.6% (694678/719360)
I (30354) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5088/16384
I (30364) OpusHttp: Found Opus audio page: 3883 bytes (page #180, total_size=3927)
I (30364) OpusHttp: Extracted Opus data: 3883 bytes (page #1 in this batch)
I (30374) OpusHttp: OGG processing result: input=4096, output=3883, pages=1, efficiency=94.8%
I (30384) OpusHttp: OGG→Opus conversion: input=4096→3883 bytes, total_efficiency=96.6% (698561/723456)
I (30394) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5257/16384
I (30394) OpusHttp: Found Opus audio page: 4039 bytes (page #181, total_size=4083)
I (30404) OpusHttp: Extracted Opus data: 4039 bytes (page #1 in this batch)
I (30414) OpusHttp: OGG processing result: input=4096, output=4039, pages=1, efficiency=98.6%
I (30424) OpusHttp: OGG→Opus conversion: input=4096→4039 bytes, total_efficiency=96.6% (702600/727552)
I (30434) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5270/16384
I (30434) OpusHttp: Found Opus audio page: 3908 bytes (page #182, total_size=3952)
I (30444) OpusHttp: Extracted Opus data: 3908 bytes (page #1 in this batch)
I (30454) OpusHttp: OGG processing result: input=4096, output=3908, pages=1, efficiency=95.4%
I (30454) OpusHttp: OGG→Opus conversion: input=4096→3908 bytes, total_efficiency=96.6% (706508/731648)
I (30474) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5414/16384
I (30474) OpusHttp: Found Opus audio page: 3887 bytes (page #183, total_size=3931)
I (30484) OpusHttp: Extracted Opus data: 3887 bytes (page #1 in this batch)
I (30484) OpusHttp: OGG processing result: input=4096, output=3887, pages=1, efficiency=94.9%
I (30494) OpusHttp: OGG→Opus conversion: input=4096→3887 bytes, total_efficiency=96.6% (710395/735744)
I (30504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5579/16384
I (30514) OpusHttp: Found Opus audio page: 4051 bytes (page #184, total_size=4095)
I (30524) OpusHttp: Extracted Opus data: 4051 bytes (page #1 in this batch)
I (30524) OpusHttp: OGG processing result: input=4096, output=4051, pages=1, efficiency=98.9%
I (30534) OpusHttp: OGG→Opus conversion: input=4096→4051 bytes, total_efficiency=96.6% (714446/739840)
I (30544) OpusHttp: OGG buffer: input=320, copy=320, buffered=1804/16384
I (30614) OpusHttp: OGG buffer: input=1440, copy=1440, buffered=3244/16384
I (30614) OpusHttp: OGG buffer: input=1440, copy=1440, buffered=4684/16384
I (30614) OpusHttp: Found Opus audio page: 4049 bytes (page #185, total_size=4093)
I (30624) OpusHttp: Extracted Opus data: 4049 bytes (page #1 in this batch)
I (30624) OpusHttp: OGG processing result: input=1440, output=4049, pages=1, efficiency=281.2%
I (30634) OpusHttp: OGG→Opus conversion: input=1440→4049 bytes, total_efficiency=96.7% (718495/743040)
I (30644) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4687/16384
I (30654) OpusHttp: Found Opus audio page: 4049 bytes (page #186, total_size=4093)
I (30654) OpusHttp: Extracted Opus data: 4049 bytes (page #1 in this batch)
I (30664) OpusHttp: OGG processing result: input=4096, output=4049, pages=1, efficiency=98.9%
I (30674) OpusHttp: OGG→Opus conversion: input=4096→4049 bytes, total_efficiency=96.7% (722544/747136)
I (30684) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4690/16384
I (30694) OpusHttp: Found Opus audio page: 3965 bytes (page #187, total_size=4009)
I (30694) OpusHttp: Extracted Opus data: 3965 bytes (page #1 in this batch)
I (30704) OpusHttp: OGG processing result: input=4096, output=3965, pages=1, efficiency=96.8%
I (30714) OpusHttp: OGG→Opus conversion: input=4096→3965 bytes, total_efficiency=96.7% (726509/751232)
I (30724) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4777/16384
I (30724) OpusHttp: Found Opus audio page: 4055 bytes (page #188, total_size=4099)
I (30734) OpusHttp: Extracted Opus data: 4055 bytes (page #1 in this batch)
I (30744) OpusHttp: OGG processing result: input=4096, output=4055, pages=1, efficiency=99.0%
I (30754) OpusHttp: OGG→Opus conversion: input=4096→4055 bytes, total_efficiency=96.7% (730564/755328)
I (30764) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4774/16384
I (30764) OpusHttp: Found Opus audio page: 4065 bytes (page #189, total_size=4109)
I (30774) OpusHttp: Extracted Opus data: 4065 bytes (page #1 in this batch)
I (30784) OpusHttp: OGG processing result: input=4096, output=4065, pages=1, efficiency=99.2%
I (30784) OpusHttp: OGG→Opus conversion: input=4096→4065 bytes, total_efficiency=96.7% (734629/759424)
I (30804) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4761/16384
I (30804) OpusHttp: Found Opus audio page: 4063 bytes (page #190, total_size=4107)
I (30814) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (30814) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (30824) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=96.7% (738692/763520)
I (30834) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4750/16384
I (30844) OpusHttp: Found Opus audio page: 4058 bytes (page #191, total_size=4102)
I (30854) OpusHttp: Extracted Opus data: 4058 bytes (page #1 in this batch)
I (30854) OpusHttp: OGG processing result: input=4096, output=4058, pages=1, efficiency=99.1%
I (30864) OpusHttp: OGG→Opus conversion: input=4096→4058 bytes, total_efficiency=96.8% (742750/767616)
I (30874) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4744/16384
I (30884) OpusHttp: Found Opus audio page: 3983 bytes (page #192, total_size=4027)
I (30884) OpusHttp: Extracted Opus data: 3983 bytes (page #1 in this batch)
I (30894) OpusHttp: OGG processing result: input=4096, output=3983, pages=1, efficiency=97.2%
I (30904) OpusHttp: OGG→Opus conversion: input=4096→3983 bytes, total_efficiency=96.8% (746733/771712)
I (30914) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4813/16384
I (30914) OpusHttp: Found Opus audio page: 4023 bytes (page #193, total_size=4067)
I (30924) OpusHttp: Extracted Opus data: 4023 bytes (page #1 in this batch)
I (30934) OpusHttp: OGG processing result: input=4096, output=4023, pages=1, efficiency=98.2%
I (30944) OpusHttp: OGG→Opus conversion: input=4096→4023 bytes, total_efficiency=96.8% (750756/775808)
I (30954) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4842/16384
I (30954) OpusHttp: Found Opus audio page: 4041 bytes (page #194, total_size=4085)
I (30964) OpusHttp: Extracted Opus data: 4041 bytes (page #1 in this batch)
I (30974) OpusHttp: OGG processing result: input=4096, output=4041, pages=1, efficiency=98.7%
I (30984) OpusHttp: OGG→Opus conversion: input=4096→4041 bytes, total_efficiency=96.8% (754797/779904)
I (30994) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4853/16384
I (30994) OpusHttp: Found Opus audio page: 4064 bytes (page #195, total_size=4108)
I (31004) OpusHttp: Extracted Opus data: 4064 bytes (page #1 in this batch)
I (31014) OpusHttp: OGG processing result: input=4096, output=4064, pages=1, efficiency=99.2%
I (31014) OpusHttp: OGG→Opus conversion: input=4096→4064 bytes, total_efficiency=96.8% (758861/784000)
I (31034) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4841/16384
I (31034) OpusHttp: Found Opus audio page: 4060 bytes (page #196, total_size=4104)
I (31044) OpusHttp: Extracted Opus data: 4060 bytes (page #1 in this batch)
I (31044) OpusHttp: OGG processing result: input=4096, output=4060, pages=1, efficiency=99.1%
I (31054) OpusHttp: OGG→Opus conversion: input=4096→4060 bytes, total_efficiency=96.8% (762921/788096)
I (31064) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4833/16384
I (31074) OpusHttp: Found Opus audio page: 4045 bytes (page #197, total_size=4089)
I (31074) OpusHttp: Extracted Opus data: 4045 bytes (page #1 in this batch)
I (31084) OpusHttp: OGG processing result: input=4096, output=4045, pages=1, efficiency=98.8%
I (31094) OpusHttp: OGG→Opus conversion: input=4096→4045 bytes, total_efficiency=96.8% (766966/792192)
I (31104) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4840/16384
I (31114) OpusHttp: Found Opus audio page: 4063 bytes (page #198, total_size=4107)
I (31114) OpusHttp: Extracted Opus data: 4063 bytes (page #1 in this batch)
I (31124) OpusHttp: OGG processing result: input=4096, output=4063, pages=1, efficiency=99.2%
I (31134) OpusHttp: OGG→Opus conversion: input=4096→4063 bytes, total_efficiency=96.8% (771029/796288)
I (31144) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4829/16384
I (31144) OpusHttp: Found Opus audio page: 4039 bytes (page #199, total_size=4083)
I (31154) OpusHttp: Extracted Opus data: 4039 bytes (page #1 in this batch)
I (31164) OpusHttp: OGG processing result: input=4096, output=4039, pages=1, efficiency=98.6%
I (31174) OpusHttp: OGG→Opus conversion: input=4096→4039 bytes, total_efficiency=96.8% (775068/800384)
I (31184) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4842/16384
I (31184) OpusHttp: Found Opus audio page: 4017 bytes (page #200, total_size=4061)
I (31194) OpusHttp: Extracted Opus data: 4017 bytes (page #1 in this batch)
I (31204) OpusHttp: OGG processing result: input=4096, output=4017, pages=1, efficiency=98.1%
I (31204) OpusHttp: OGG→Opus conversion: input=4096→4017 bytes, total_efficiency=96.8% (779085/804480)
I (31214) OpusHttp: OGG buffer: input=480, copy=480, buffered=1261/16384
I (31234) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5357/16384
I (31234) OpusHttp: Found Opus audio page: 4065 bytes (page #201, total_size=4109)
I (31234) OpusHttp: Extracted Opus data: 4065 bytes (page #1 in this batch)
I (31244) OpusHttp: OGG processing result: input=4096, output=4065, pages=1, efficiency=99.2%
I (31254) OpusHttp: OGG→Opus conversion: input=4096→4065 bytes, total_efficiency=96.8% (783150/809056)
I (31264) OpusHttp: OGG buffer: input=224, copy=224, buffered=1472/16384
I (31274) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5568/16384
I (31274) OpusHttp: Found Opus audio page: 4062 bytes (page #202, total_size=4106)
I (31284) OpusHttp: Extracted Opus data: 4062 bytes (page #1 in this batch)
I (31294) OpusHttp: OGG processing result: input=4096, output=4062, pages=1, efficiency=99.2%
I (31294) OpusHttp: OGG→Opus conversion: input=4096→4062 bytes, total_efficiency=96.8% (787212/813376)
I (31314) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5558/16384
I (31314) OpusHttp: Found Opus audio page: 3904 bytes (page #203, total_size=3948)
I (31324) OpusHttp: Extracted Opus data: 3904 bytes (page #1 in this batch)
I (31324) OpusHttp: OGG processing result: input=4096, output=3904, pages=1, efficiency=95.3%
I (31334) OpusHttp: OGG→Opus conversion: input=4096→3904 bytes, total_efficiency=96.8% (791116/817472)
I (31344) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5706/16384
I (31354) OpusHttp: Found Opus audio page: 3989 bytes (page #204, total_size=4033)
I (31364) OpusHttp: Extracted Opus data: 3989 bytes (page #1 in this batch)
I (31364) OpusHttp: OGG processing result: input=4096, output=3989, pages=1, efficiency=97.4%
I (31374) OpusHttp: OGG→Opus conversion: input=4096→3989 bytes, total_efficiency=96.8% (795105/821568)
I (31384) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5769/16384
I (31394) OpusHttp: Found Opus audio page: 4005 bytes (page #205, total_size=4049)
I (31394) OpusHttp: Extracted Opus data: 4005 bytes (page #1 in this batch)
I (31404) OpusHttp: OGG processing result: input=4096, output=4005, pages=1, efficiency=97.8%
I (31414) OpusHttp: OGG→Opus conversion: input=4096→4005 bytes, total_efficiency=96.8% (799110/825664)
I (31424) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5816/16384
I (31424) OpusHttp: Found Opus audio page: 3895 bytes (page #206, total_size=3939)
I (31434) OpusHttp: Extracted Opus data: 3895 bytes (page #1 in this batch)
I (31444) OpusHttp: OGG processing result: input=4096, output=3895, pages=1, efficiency=95.1%
I (31454) OpusHttp: OGG→Opus conversion: input=4096→3895 bytes, total_efficiency=96.8% (803005/829760)
I (31464) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5973/16384
I (31464) OpusHttp: Found Opus audio page: 3974 bytes (page #207, total_size=4018)
I (31474) OpusHttp: Extracted Opus data: 3974 bytes (page #1 in this batch)
I (31484) OpusHttp: OGG processing result: input=4096, output=3974, pages=1, efficiency=97.0%
I (31484) OpusHttp: OGG→Opus conversion: input=4096→3974 bytes, total_efficiency=96.8% (806979/833856)
I (31504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6051/16384
I (31504) OpusHttp: Found Opus audio page: 4037 bytes (page #208, total_size=4081)
I (31514) OpusHttp: Extracted Opus data: 4037 bytes (page #1 in this batch)
I (31514) OpusHttp: OGG processing result: input=4096, output=4037, pages=1, efficiency=98.6%
I (31524) OpusHttp: OGG→Opus conversion: input=4096→4037 bytes, total_efficiency=96.8% (811016/837952)
I (31534) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6066/16384
I (31544) OpusHttp: Found Opus audio page: 4044 bytes (page #209, total_size=4088)
I (31554) OpusHttp: Extracted Opus data: 4044 bytes (page #1 in this batch)
I (31554) OpusHttp: OGG processing result: input=4096, output=4044, pages=1, efficiency=98.7%
I (31564) OpusHttp: OGG→Opus conversion: input=4096→4044 bytes, total_efficiency=96.8% (815060/842048)
I (31574) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6074/16384
I (31584) OpusHttp: Found Opus audio page: 3882 bytes (page #210, total_size=3926)
I (31584) OpusHttp: Extracted Opus data: 3882 bytes (page #1 in this batch)
I (31594) OpusHttp: OGG processing result: input=4096, output=3882, pages=1, efficiency=94.8%
I (31604) OpusHttp: OGG→Opus conversion: input=4096→3882 bytes, total_efficiency=96.8% (818942/846144)
I (31614) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6244/16384
I (31624) OpusHttp: Found Opus audio page: 3996 bytes (page #211, total_size=4040)
I (31624) OpusHttp: Extracted Opus data: 3996 bytes (page #1 in this batch)
I (31634) OpusHttp: OGG processing result: input=4096, output=3996, pages=1, efficiency=97.6%
I (31644) OpusHttp: OGG→Opus conversion: input=4096→3996 bytes, total_efficiency=96.8% (822938/850240)
I (31654) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6300/16384
I (31654) OpusHttp: Found Opus audio page: 3847 bytes (page #212, total_size=3891)
I (31664) OpusHttp: Extracted Opus data: 3847 bytes (page #1 in this batch)
I (31674) OpusHttp: OGG processing result: input=4096, output=3847, pages=1, efficiency=93.9%
I (31684) OpusHttp: OGG→Opus conversion: input=4096→3847 bytes, total_efficiency=96.8% (826785/854336)
I (31694) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6505/16384
I (31694) OpusHttp: Found Opus audio page: 3959 bytes (page #213, total_size=4003)
I (31704) OpusHttp: Extracted Opus data: 3959 bytes (page #1 in this batch)
I (31714) OpusHttp: OGG processing result: input=4096, output=3959, pages=1, efficiency=96.7%
I (31714) OpusHttp: OGG→Opus conversion: input=4096→3959 bytes, total_efficiency=96.8% (830744/858432)
I (31734) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6598/16384
I (31734) OpusHttp: Found Opus audio page: 3941 bytes (page #214, total_size=3985)
I (31744) OpusHttp: Extracted Opus data: 3941 bytes (page #1 in this batch)
I (31744) OpusHttp: OGG processing result: input=4096, output=3941, pages=1, efficiency=96.2%
I (31754) OpusHttp: OGG→Opus conversion: input=4096→3941 bytes, total_efficiency=96.8% (834685/862528)
I (31764) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6709/16384
I (31774) OpusHttp: Found Opus audio page: 3840 bytes (page #215, total_size=3884)
I (31784) OpusHttp: Extracted Opus data: 3840 bytes (page #1 in this batch)
I (31784) OpusHttp: OGG processing result: input=4096, output=3840, pages=1, efficiency=93.8%
I (31794) OpusHttp: OGG→Opus conversion: input=4096→3840 bytes, total_efficiency=96.8% (838525/866624)
I (31804) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6921/16384
I (31814) OpusHttp: Found Opus audio page: 3868 bytes (page #216, total_size=3912)
I (31814) OpusHttp: Extracted Opus data: 3868 bytes (page #1 in this batch)
I (31824) OpusHttp: OGG processing result: input=4096, output=3868, pages=1, efficiency=94.4%
I (31834) OpusHttp: OGG→Opus conversion: input=4096→3868 bytes, total_efficiency=96.7% (842393/870720)
I (31844) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7105/16384
I (31844) OpusHttp: Found Opus audio page: 3859 bytes (page #217, total_size=3903)
I (31854) OpusHttp: Extracted Opus data: 3859 bytes (page #1 in this batch)
I (31864) OpusHttp: OGG processing result: input=4096, output=3859, pages=1, efficiency=94.2%
I (31874) OpusHttp: OGG→Opus conversion: input=4096→3859 bytes, total_efficiency=96.7% (846252/874816)
I (31884) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7298/16384
I (31884) OpusHttp: Found Opus audio page: 3920 bytes (page #218, total_size=3964)
I (31894) OpusHttp: Extracted Opus data: 3920 bytes (page #1 in this batch)
I (31904) OpusHttp: OGG processing result: input=4096, output=3920, pages=1, efficiency=95.7%
I (31904) OpusHttp: OGG→Opus conversion: input=4096→3920 bytes, total_efficiency=96.7% (850172/878912)
I (31924) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7430/16384
I (31924) OpusHttp: Found Opus audio page: 3872 bytes (page #219, total_size=3916)
I (31934) OpusHttp: Extracted Opus data: 3872 bytes (page #1 in this batch)
I (31934) OpusHttp: OGG processing result: input=4096, output=3872, pages=1, efficiency=94.5%
I (31944) OpusHttp: OGG→Opus conversion: input=4096→3872 bytes, total_efficiency=96.7% (854044/883008)
I (31954) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7610/16384
I (31964) OpusHttp: Found Opus audio page: 3957 bytes (page #220, total_size=4001)
I (31974) OpusHttp: Extracted Opus data: 3957 bytes (page #1 in this batch)
I (31974) OpusHttp: OGG processing result: input=4096, output=3957, pages=1, efficiency=96.6%
I (31984) OpusHttp: OGG→Opus conversion: input=4096→3957 bytes, total_efficiency=96.7% (858001/887104)
I (31994) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7705/16384
I (32004) OpusHttp: Found Opus audio page: 3920 bytes (page #221, total_size=3964)
I (32004) OpusHttp: Extracted Opus data: 3920 bytes (page #1 in this batch)
I (32014) OpusHttp: OGG processing result: input=4096, output=3920, pages=1, efficiency=95.7%
I (32024) OpusHttp: OGG→Opus conversion: input=4096→3920 bytes, total_efficiency=96.7% (861921/891200)
I (32034) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7837/16384
I (32044) OpusHttp: Found Opus audio page: 3981 bytes (page #222, total_size=4025)
I (32044) OpusHttp: Extracted Opus data: 3981 bytes (page #1 in this batch)
I (32054) OpusHttp: OGG processing result: input=4096, output=3981, pages=1, efficiency=97.2%
I (32064) OpusHttp: OGG→Opus conversion: input=4096→3981 bytes, total_efficiency=96.7% (865902/895296)
I (32074) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7908/16384
I (32074) OpusHttp: Found Opus audio page: 3967 bytes (page #223, total_size=4011)
I (32084) OpusHttp: Extracted Opus data: 3967 bytes (page #1 in this batch)
I (32094) OpusHttp: OGG processing result: input=4096, output=3967, pages=1, efficiency=96.9%
I (32104) OpusHttp: OGG→Opus conversion: input=4096→3967 bytes, total_efficiency=96.7% (869869/899392)
I (32114) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7993/16384
I (32114) OpusHttp: Found Opus audio page: 3861 bytes (page #224, total_size=3905)
I (32124) OpusHttp: Extracted Opus data: 3861 bytes (page #1 in this batch)
I (32134) OpusHttp: Found Opus audio page: 3786 bytes (page #225, total_size=3830)
I (32134) OpusHttp: OGG processing result: input=4096, output=3861, pages=1, efficiency=94.3%
I (32144) OpusHttp: OGG→Opus conversion: input=4096→3861 bytes, total_efficiency=96.7% (873730/903488)
I (32154) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4354/16384
I (32164) OpusHttp: Found Opus audio page: 3913 bytes (page #226, total_size=3957)
I (32164) OpusHttp: Extracted Opus data: 3913 bytes (page #1 in this batch)
I (32174) OpusHttp: OGG processing result: input=4096, output=3913, pages=1, efficiency=95.5%
I (32184) OpusHttp: OGG→Opus conversion: input=4096→3913 bytes, total_efficiency=96.7% (877643/907584)
I (32194) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4493/16384
I (32204) OpusHttp: Found Opus audio page: 3993 bytes (page #227, total_size=4037)
I (32204) OpusHttp: Extracted Opus data: 3993 bytes (page #1 in this batch)
I (32214) OpusHttp: OGG processing result: input=4096, output=3993, pages=1, efficiency=97.5%
I (32224) OpusHttp: OGG→Opus conversion: input=4096→3993 bytes, total_efficiency=96.7% (881636/911680)
I (32234) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4552/16384
I (32234) OpusHttp: Found Opus audio page: 3921 bytes (page #228, total_size=3965)
I (32244) OpusHttp: Extracted Opus data: 3921 bytes (page #1 in this batch)
I (32254) OpusHttp: OGG processing result: input=4096, output=3921, pages=1, efficiency=95.7%
I (32264) OpusHttp: OGG→Opus conversion: input=4096→3921 bytes, total_efficiency=96.7% (885557/915776)
I (32274) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4683/16384
I (32274) OpusHttp: Found Opus audio page: 3984 bytes (page #229, total_size=4028)
I (32284) OpusHttp: Extracted Opus data: 3984 bytes (page #1 in this batch)
I (32294) OpusHttp: OGG processing result: input=4096, output=3984, pages=1, efficiency=97.3%
I (32294) OpusHttp: OGG→Opus conversion: input=4096→3984 bytes, total_efficiency=96.7% (889541/919872)
I (32304) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4751/16384
I (32314) OpusHttp: Found Opus audio page: 3969 bytes (page #230, total_size=4013)
I (32324) OpusHttp: Extracted Opus data: 3969 bytes (page #1 in this batch)
I (32324) OpusHttp: OGG processing result: input=4096, output=3969, pages=1, efficiency=96.9%
I (32334) OpusHttp: OGG→Opus conversion: input=4096→3969 bytes, total_efficiency=96.7% (893510/923968)
I (32344) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4834/16384
I (32354) OpusHttp: Found Opus audio page: 3918 bytes (page #231, total_size=3962)
I (32364) OpusHttp: Extracted Opus data: 3918 bytes (page #1 in this batch)
I (32364) OpusHttp: OGG processing result: input=4096, output=3918, pages=1, efficiency=95.7%
I (32374) OpusHttp: OGG→Opus conversion: input=4096→3918 bytes, total_efficiency=96.7% (897428/928064)
I (32384) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4968/16384
I (32394) OpusHttp: Found Opus audio page: 3830 bytes (page #232, total_size=3874)
I (32394) OpusHttp: Extracted Opus data: 3830 bytes (page #1 in this batch)
I (32404) OpusHttp: OGG processing result: input=4096, output=3830, pages=1, efficiency=93.5%
I (32414) OpusHttp: OGG→Opus conversion: input=4096→3830 bytes, total_efficiency=96.7% (901258/932160)
I (32424) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5190/16384
I (32424) OpusHttp: Found Opus audio page: 3888 bytes (page #233, total_size=3932)
I (32434) OpusHttp: Extracted Opus data: 3888 bytes (page #1 in this batch)
I (32444) OpusHttp: OGG processing result: input=4096, output=3888, pages=1, efficiency=94.9%
I (32454) OpusHttp: OGG→Opus conversion: input=4096→3888 bytes, total_efficiency=96.7% (905146/936256)
I (32464) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5354/16384
I (32464) OpusHttp: Found Opus audio page: 3897 bytes (page #234, total_size=3941)
I (32474) OpusHttp: Extracted Opus data: 3897 bytes (page #1 in this batch)
I (32484) OpusHttp: OGG processing result: input=4096, output=3897, pages=1, efficiency=95.1%
I (32484) OpusHttp: OGG→Opus conversion: input=4096→3897 bytes, total_efficiency=96.7% (909043/940352)
I (32504) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5509/16384
I (32504) OpusHttp: Found Opus audio page: 3911 bytes (page #235, total_size=3955)
I (32514) OpusHttp: Extracted Opus data: 3911 bytes (page #1 in this batch)
I (32514) OpusHttp: OGG processing result: input=4096, output=3911, pages=1, efficiency=95.5%
I (32524) OpusHttp: OGG→Opus conversion: input=4096→3911 bytes, total_efficiency=96.7% (912954/944448)
I (32534) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5650/16384
I (32544) OpusHttp: Found Opus audio page: 3881 bytes (page #236, total_size=3925)
I (32554) OpusHttp: Extracted Opus data: 3881 bytes (page #1 in this batch)
I (32554) OpusHttp: OGG processing result: input=4096, output=3881, pages=1, efficiency=94.8%
I (32564) OpusHttp: OGG→Opus conversion: input=4096→3881 bytes, total_efficiency=96.7% (916835/948544)
I (32574) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5821/16384
I (32584) OpusHttp: Found Opus audio page: 3891 bytes (page #237, total_size=3935)
I (32584) OpusHttp: Extracted Opus data: 3891 bytes (page #1 in this batch)
I (32594) OpusHttp: OGG processing result: input=4096, output=3891, pages=1, efficiency=95.0%
I (32604) OpusHttp: OGG→Opus conversion: input=4096→3891 bytes, total_efficiency=96.6% (920726/952640)
I (32614) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5982/16384
I (32624) OpusHttp: Found Opus audio page: 3969 bytes (page #238, total_size=4013)
I (32624) OpusHttp: Extracted Opus data: 3969 bytes (page #1 in this batch)
I (32634) OpusHttp: OGG processing result: input=4096, output=3969, pages=1, efficiency=96.9%
I (32644) OpusHttp: OGG→Opus conversion: input=4096→3969 bytes, total_efficiency=96.7% (924695/956736)
I (32654) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6065/16384
I (32654) OpusHttp: Found Opus audio page: 3921 bytes (page #239, total_size=3965)
I (32664) OpusHttp: Extracted Opus data: 3921 bytes (page #1 in this batch)
I (32674) OpusHttp: OGG processing result: input=4096, output=3921, pages=1, efficiency=95.7%
I (32684) OpusHttp: OGG→Opus conversion: input=4096→3921 bytes, total_efficiency=96.6% (928616/960832)
I (32694) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6196/16384
I (32694) OpusHttp: Found Opus audio page: 3946 bytes (page #240, total_size=3990)
I (32704) OpusHttp: Extracted Opus data: 3946 bytes (page #1 in this batch)
I (32714) OpusHttp: OGG processing result: input=4096, output=3946, pages=1, efficiency=96.3%
I (32714) OpusHttp: OGG→Opus conversion: input=4096→3946 bytes, total_efficiency=96.6% (932562/964928)
I (32734) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6302/16384
I (32734) OpusHttp: Found Opus audio page: 3754 bytes (page #241, total_size=3798)
I (32744) OpusHttp: Extracted Opus data: 3754 bytes (page #1 in this batch)
I (32744) OpusHttp: OGG processing result: input=4096, output=3754, pages=1, efficiency=91.7%
I (32754) OpusHttp: OGG→Opus conversion: input=4096→3754 bytes, total_efficiency=96.6% (936316/969024)
I (32764) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6600/16384
I (32774) OpusHttp: Found Opus audio page: 3708 bytes (page #242, total_size=3752)
I (32784) OpusHttp: Extracted Opus data: 3708 bytes (page #1 in this batch)
I (32784) OpusHttp: OGG processing result: input=4096, output=3708, pages=1, efficiency=90.5%
I (32794) OpusHttp: OGG→Opus conversion: input=4096→3708 bytes, total_efficiency=96.6% (940024/973120)
I (32804) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=6944/16384
I (32814) OpusHttp: Found Opus audio page: 3840 bytes (page #243, total_size=3884)
I (32814) OpusHttp: Extracted Opus data: 3840 bytes (page #1 in this batch)
I (32824) OpusHttp: OGG processing result: input=4096, output=3840, pages=1, efficiency=93.8%
I (32834) OpusHttp: OGG→Opus conversion: input=4096→3840 bytes, total_efficiency=96.6% (943864/977216)
I (32844) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7156/16384
I (32844) OpusHttp: Found Opus audio page: 4044 bytes (page #244, total_size=4088)
I (32854) OpusHttp: Extracted Opus data: 4044 bytes (page #1 in this batch)
I (32864) OpusHttp: OGG processing result: input=4096, output=4044, pages=1, efficiency=98.7%
I (32874) OpusHttp: OGG→Opus conversion: input=4096→4044 bytes, total_efficiency=96.6% (947908/981312)
I (32884) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7164/16384
I (32884) OpusHttp: Found Opus audio page: 4036 bytes (page #245, total_size=4080)
I (32894) OpusHttp: Extracted Opus data: 4036 bytes (page #1 in this batch)
I (32904) OpusHttp: OGG processing result: input=4096, output=4036, pages=1, efficiency=98.5%
I (32904) OpusHttp: OGG→Opus conversion: input=4096→4036 bytes, total_efficiency=96.6% (951944/985408)
I (32924) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7180/16384
I (32924) OpusHttp: Found Opus audio page: 4005 bytes (page #246, total_size=4049)
I (32934) OpusHttp: Extracted Opus data: 4005 bytes (page #1 in this batch)
I (32944) OpusHttp: OGG processing result: input=4096, output=4005, pages=1, efficiency=97.8%
I (32944) OpusHttp: OGG→Opus conversion: input=4096→4005 bytes, total_efficiency=96.6% (955949/989504)
I (32954) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7227/16384
I (32964) OpusHttp: Found Opus audio page: 4008 bytes (page #247, total_size=4052)
I (32974) OpusHttp: Extracted Opus data: 4008 bytes (page #1 in this batch)
I (32974) OpusHttp: OGG processing result: input=4096, output=4008, pages=1, efficiency=97.9%
I (32984) OpusHttp: OGG→Opus conversion: input=4096→4008 bytes, total_efficiency=96.6% (959957/993600)
I (32994) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7271/16384
I (33004) OpusHttp: Found Opus audio page: 4032 bytes (page #248, total_size=4076)
I (33004) OpusHttp: Extracted Opus data: 4032 bytes (page #1 in this batch)
I (33014) OpusHttp: OGG processing result: input=4096, output=4032, pages=1, efficiency=98.4%
I (33024) OpusHttp: OGG→Opus conversion: input=4096→4032 bytes, total_efficiency=96.6% (963989/997696)
I (33034) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7291/16384
I (33044) OpusHttp: Found Opus audio page: 3954 bytes (page #249, total_size=3998)
I (33044) OpusHttp: Extracted Opus data: 3954 bytes (page #1 in this batch)
I (33054) OpusHttp: OGG processing result: input=4096, output=3954, pages=1, efficiency=96.5%
I (33064) OpusHttp: OGG→Opus conversion: input=4096→3954 bytes, total_efficiency=96.6% (967943/1001792)
I (33074) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7389/16384
I (33074) OpusHttp: Found Opus audio page: 4015 bytes (page #250, total_size=4059)
I (33084) OpusHttp: Extracted Opus data: 4015 bytes (page #1 in this batch)
I (33094) OpusHttp: OGG processing result: input=4096, output=4015, pages=1, efficiency=98.0%
I (33104) OpusHttp: OGG→Opus conversion: input=4096→4015 bytes, total_efficiency=96.6% (971958/1005888)
I (33114) OpusHttp: OGG buffer: input=3552, copy=3552, buffered=6882/16384
I (33114) OpusHttp: Found Opus audio page: 3900 bytes (page #251, total_size=3944)
I (33124) OpusHttp: Extracted Opus data: 3900 bytes (page #1 in this batch)
I (33134) OpusHttp: OGG processing result: input=3552, output=3900, pages=1, efficiency=109.8%
I (33134) OpusHttp: OGG→Opus conversion: input=3552→3900 bytes, total_efficiency=96.7% (975858/1009440)
I (33154) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7034/16384
I (33154) OpusHttp: Found Opus audio page: 4049 bytes (page #252, total_size=4093)
I (33164) OpusHttp: Extracted Opus data: 4049 bytes (page #1 in this batch)
I (33164) OpusHttp: OGG processing result: input=4096, output=4049, pages=1, efficiency=98.9%
I (33174) OpusHttp: OGG→Opus conversion: input=4096→4049 bytes, total_efficiency=96.7% (979907/1013536)
I (33184) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7037/16384
I (33194) OpusHttp: Found Opus audio page: 3957 bytes (page #253, total_size=4001)
I (33204) OpusHttp: Extracted Opus data: 3957 bytes (page #1 in this batch)
I (33204) OpusHttp: OGG processing result: input=4096, output=3957, pages=1, efficiency=96.6%
I (33214) OpusHttp: OGG→Opus conversion: input=4096→3957 bytes, total_efficiency=96.7% (983864/1017632)
I (33224) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7132/16384
I (33234) OpusHttp: Found Opus audio page: 3946 bytes (page #254, total_size=3990)
I (33234) OpusHttp: Extracted Opus data: 3946 bytes (page #1 in this batch)
I (33244) OpusHttp: OGG processing result: input=4096, output=3946, pages=1, efficiency=96.3%
I (33254) OpusHttp: OGG→Opus conversion: input=4096→3946 bytes, total_efficiency=96.7% (987810/1021728)
I (33264) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7238/16384
I (33274) OpusHttp: Found Opus audio page: 3932 bytes (page #255, total_size=3976)
I (33274) OpusHttp: Extracted Opus data: 3932 bytes (page #1 in this batch)
I (33284) OpusHttp: OGG processing result: input=4096, output=3932, pages=1, efficiency=96.0%
I (33294) OpusHttp: OGG→Opus conversion: input=4096→3932 bytes, total_efficiency=96.7% (991742/1025824)
I (33304) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7358/16384
I (33304) OpusHttp: Found Opus audio page: 4029 bytes (page #256, total_size=4073)
I (33314) OpusHttp: Extracted Opus data: 4029 bytes (page #1 in this batch)
I (33324) OpusHttp: OGG processing result: input=4096, output=4029, pages=1, efficiency=98.4%
I (33334) OpusHttp: OGG→Opus conversion: input=4096→4029 bytes, total_efficiency=96.7% (995771/1029920)
I (33344) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7381/16384
I (33344) OpusHttp: Found Opus audio page: 3952 bytes (page #257, total_size=3996)
I (33354) OpusHttp: Extracted Opus data: 3952 bytes (page #1 in this batch)
I (33364) OpusHttp: OGG processing result: input=4096, output=3952, pages=1, efficiency=96.5%
I (33364) OpusHttp: OGG→Opus conversion: input=4096→3952 bytes, total_efficiency=96.7% (999723/1034016)
I (33374) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7481/16384
I (33384) OpusHttp: Found Opus audio page: 4007 bytes (page #258, total_size=4051)
I (33394) OpusHttp: Extracted Opus data: 4007 bytes (page #1 in this batch)
I (33394) OpusHttp: OGG processing result: input=4096, output=4007, pages=1, efficiency=97.8%
I (33404) OpusHttp: OGG→Opus conversion: input=4096→4007 bytes, total_efficiency=96.7% (1003730/1038112)
I (33414) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7526/16384
I (33424) OpusHttp: Found Opus audio page: 3986 bytes (page #259, total_size=4030)
I (33434) OpusHttp: Extracted Opus data: 3986 bytes (page #1 in this batch)
I (33434) OpusHttp: OGG processing result: input=4096, output=3986, pages=1, efficiency=97.3%
I (33444) OpusHttp: OGG→Opus conversion: input=4096→3986 bytes, total_efficiency=96.7% (1007716/1042208)
I (33454) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7592/16384
I (33464) OpusHttp: Found Opus audio page: 4029 bytes (page #260, total_size=4073)
I (33464) OpusHttp: Extracted Opus data: 4029 bytes (page #1 in this batch)
I (33474) OpusHttp: OGG processing result: input=4096, output=4029, pages=1, efficiency=98.4%
I (33484) OpusHttp: OGG→Opus conversion: input=4096→4029 bytes, total_efficiency=96.7% (1011745/1046304)
I (33484) OpusHttp: Audio callback called 101 times, playing=1, ringbuf=0x3fcd2b28
I (33504) OpusHttp: Opus decode failed: 2 (len=964, buffer_available=0)
I (33494) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7615/16384
I (33514) OpusHttp: Found Opus audio page: 4041 bytes (page #261, total_size=4085)
I (33524) OpusHttp: Extracted Opus data: 4041 bytes (page #1 in this batch)
I (33524) OpusHttp: OGG processing result: input=4096, output=4041, pages=1, efficiency=98.7%
I (33534) OpusHttp: OGG→Opus conversion: input=4096→4041 bytes, total_efficiency=96.7% (1015786/1050400)
I (33544) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7626/16384
I (33554) OpusHttp: Found Opus audio page: 4059 bytes (page #262, total_size=4103)
I (33554) OpusHttp: Extracted Opus data: 4059 bytes (page #1 in this batch)
I (33564) OpusHttp: OGG processing result: input=4096, output=4059, pages=1, efficiency=99.1%
I (33574) OpusHttp: OGG→Opus conversion: input=4096→4059 bytes, total_efficiency=96.7% (1019845/1054496)
I (33584) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7619/16384
I (33594) OpusHttp: Found Opus audio page: 3909 bytes (page #263, total_size=3953)
I (33594) OpusHttp: Extracted Opus data: 3909 bytes (page #1 in this batch)
I (33604) OpusHttp: OGG processing result: input=4096, output=3909, pages=1, efficiency=95.4%
I (33614) OpusHttp: OGG→Opus conversion: input=4096→3909 bytes, total_efficiency=96.7% (1023754/1058592)
I (33604) OpusHttp: No PCM data available
I (33624) SkOpusDec:  DEC: RxCnt=0, RxShortCnt=0, RxLongCnt=0, PlayFailCnt=0, PlayCnt=175, EqCnt=0, LTry=175, LCnt=175, lReq=2, rReq=0
I (33624) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7762/16384
I (33644) OpusHttp: Found Opus audio page: 4001 bytes (page #264, total_size=4045)
I (33654) OpusHttp: Extracted Opus data: 4001 bytes (page #1 in this batch)
I (33654) OpusHttp: OGG processing result: input=4096, output=4001, pages=1, efficiency=97.7%
I (33664) OpusHttp: OGG→Opus conversion: input=4096→4001 bytes, total_efficiency=96.7% (1027755/1062688)
I (33674) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7813/16384
I (33684) OpusHttp: Found Opus audio page: 4024 bytes (page #265, total_size=4068)
I (33694) OpusHttp: Extracted Opus data: 4024 bytes (page #1 in this batch)
I (33694) OpusHttp: OGG processing result: input=4096, output=4024, pages=1, efficiency=98.2%
I (33704) OpusHttp: OGG→Opus conversion: input=4096→4024 bytes, total_efficiency=96.7% (1031779/1066784)
I (33714) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7841/16384
I (33724) OpusHttp: Found Opus audio page: 4001 bytes (page #266, total_size=4045)
I (33724) OpusHttp: Extracted Opus data: 4001 bytes (page #1 in this batch)
I (33734) OpusHttp: OGG processing result: input=4096, output=4001, pages=1, efficiency=97.7%
I (33744) OpusHttp: OGG→Opus conversion: input=4096→4001 bytes, total_efficiency=96.7% (1035780/1070880)
I (33754) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7892/16384
I (33754) OpusHttp: Found Opus audio page: 4029 bytes (page #267, total_size=4073)
I (33764) OpusHttp: Extracted Opus data: 4029 bytes (page #1 in this batch)
I (33774) OpusHttp: OGG processing result: input=4096, output=4029, pages=1, efficiency=98.4%
I (33784) OpusHttp: OGG→Opus conversion: input=4096→4029 bytes, total_efficiency=96.7% (1039809/1074976)
I (33794) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7915/16384
I (33794) OpusHttp: Found Opus audio page: 3997 bytes (page #268, total_size=4041)
I (33804) OpusHttp: Extracted Opus data: 3997 bytes (page #1 in this batch)
I (33814) OpusHttp: OGG processing result: input=4096, output=3997, pages=1, efficiency=97.6%
I (33824) OpusHttp: OGG→Opus conversion: input=4096→3997 bytes, total_efficiency=96.7% (1043806/1079072)
I (33834) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=7970/16384
I (33834) OpusHttp: Found Opus audio page: 3956 bytes (page #269, total_size=4000)
I (33844) OpusHttp: Extracted Opus data: 3956 bytes (page #1 in this batch)
I (33854) OpusHttp: Found Opus audio page: 3429 bytes (page #270, total_size=3473)
I (33854) OpusHttp: OGG processing result: input=4096, output=3956, pages=1, efficiency=96.6%
I (33864) OpusHttp: OGG→Opus conversion: input=4096→3956 bytes, total_efficiency=96.7% (1047762/1083168)
I (33874) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=4593/16384
I (33884) OpusHttp: Found Opus audio page: 3061 bytes (page #271, total_size=3105)
I (33884) OpusHttp: Extracted Opus data: 3061 bytes (page #1 in this batch)
I (33894) OpusHttp: OGG processing result: input=4096, output=3061, pages=1, efficiency=74.7%
I (33904) OpusHttp: OGG→Opus conversion: input=4096→3061 bytes, total_efficiency=96.6% (1050823/1087264)
I (33914) OpusHttp: OGG buffer: input=4096, copy=4096, buffered=5584/16384
I (33924) OpusHttp: Found Opus audio page: 2691 bytes (page #272, total_size=2735)
I (33924) OpusHttp: Extracted Opus data: 2691 bytes (page #1 in this batch)
I (33934) OpusHttp: Found Opus audio page: 2620 bytes (page #273, total_size=2664)
I (33944) OpusHttp: OGG processing result: input=4096, output=2691, pages=1, efficiency=65.7%
I (33944) OpusHttp: OGG→Opus conversion: input=4096→2691 bytes, total_efficiency=96.5% (1053514/1091360)
I (33964) OpusHttp: OGG buffer: input=655, copy=655, buffered=840/16384
I (33964) OpusHttp: Found Opus audio page: 808 bytes (page #274, total_size=840)
I (33974) OpusHttp: Extracted Opus data: 808 bytes (page #1 in this batch)
I (33974) OpusHttp: OGG processing result: input=655, output=808, pages=1, efficiency=123.4%
I (33984) OpusHttp: OGG→Opus conversion: input=655→808 bytes, total_efficiency=96.5% (1054322/1092015)
I (33994) OpusHttp: Download completed: 1092015 bytes
I (34154) OpusHttp: Playback finished - Stats: downloaded=1092015, ogg_input=1092015, opus_output=1054322, efficiency=96.5%, pages_processed=274, pages_skipped=2
I (39774) OpusHttp: Audio callback called 201 times, playing=0, ringbuf=0x3fcd2b28
I (45774) OpusHttp: Audio callback called 301 times, playing=0, ringbuf=0x3fcd2b28